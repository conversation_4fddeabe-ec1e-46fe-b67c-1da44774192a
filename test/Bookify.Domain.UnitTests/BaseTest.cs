using Bookify.Domain.Entities.Abstractions;
using Bookify.Domain.UnitTests.TestUtilities;

namespace Bookify.Domain.UnitTests;

public abstract class BaseTest
{
    /// <summary>
    /// Legacy method for backward compatibility - use CustomAssertions.ShouldHavePublishedDomainEvent instead
    /// </summary>
    [Obsolete("Use entity.ShouldHavePublishedDomainEvent<T>() from CustomAssertions instead")]
    public static T AssertDomainEventWasPublished<T>(IEntity entity)
        where T : IDomainEvent
    {
        return entity.ShouldHavePublishedDomainEvent<T>();
    }

    /// <summary>
    /// Helper method to get the base test date time for consistent testing
    /// </summary>
    protected static DateTime BaseTestDateTime => TestHelpers.DateTimeHelpers.BaseTestDateTime;

    /// <summary>
    /// Helper method to create test date ranges
    /// </summary>
    protected static DateRange CreateTestDateRange(int startDaysFromNow, int durationDays)
    {
        return TestHelpers.DateTimeHelpers.CreateDateRange(startDaysFromNow, durationDays);
    }

    /// <summary>
    /// Helper method to clear domain events from an entity
    /// </summary>
    protected static void ClearDomainEvents(IEntity entity)
    {
        TestHelpers.DomainEventHelpers.ClearDomainEvents(entity);
    }
}