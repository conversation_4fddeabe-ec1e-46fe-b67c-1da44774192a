using Bookify.Domain.Entities.Apartments;
using Bookify.Domain.Entities.Apartments.Enums;
using Bookify.Domain.Entities.Apartments.ValueObjects;
using Bookify.Domain.Entities.Bookings;
using Bookify.Domain.Entities.Bookings.ValueObjects;
using Bookify.Domain.Entities.Users;
using Bookify.Domain.Entities.Users.ValueObjects;
using Bookify.Domain.Shared;
using Bookify.Domain.UnitTests.TestBuilders;

namespace Bookify.Domain.UnitTests.TestUtilities;

/// <summary>
/// Factory for creating test data with predefined scenarios
/// </summary>
public static class TestDataFactory
{
    /// <summary>
    /// Common test scenarios for bookings
    /// </summary>
    public static class Bookings
    {
        /// <summary>
        /// Creates a typical weekend booking scenario
        /// </summary>
        public static Booking WeekendBooking()
        {
            var startDate = GetNextFriday();
            var endDate = startDate.AddDays(2); // Friday to Sunday
            
            return BookingTestDataBuilder.Create()
                .WithDuration(startDate, endDate)
                .WithApartment(new Money(150.0m, Currency.Usd), new Money(75.0m, Currency.Usd))
                .BuildReserved();
        }

        /// <summary>
        /// Creates a long-term booking scenario (1 month)
        /// </summary>
        public static Booking LongTermBooking()
        {
            var startDate = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(30));
            var endDate = startDate.AddDays(30);
            
            return BookingTestDataBuilder.Create()
                .WithDuration(startDate, endDate)
                .WithApartment(new Money(80.0m, Currency.Usd), Money.Zero()) // No cleaning fee for long term
                .BuildConfirmed();
        }

        /// <summary>
        /// Creates a luxury booking scenario
        /// </summary>
        public static Booking LuxuryBooking()
        {
            var apartment = ApartmentTestDataBuilder.CreateLuxury();
            var user = UserTestDataBuilder.CreateWithName("Premium", "Guest");
            
            return BookingTestDataBuilder.Create()
                .WithApartment(apartment)
                .WithUser(user)
                .WithFutureDates(14)
                .BuildConfirmed();
        }

        /// <summary>
        /// Creates a budget booking scenario
        /// </summary>
        public static Booking BudgetBooking()
        {
            var apartment = ApartmentTestDataBuilder.CreateBudget();
            var user = UserTestDataBuilder.CreateWithName("Budget", "Traveler");
            
            return BookingTestDataBuilder.Create()
                .WithApartment(apartment)
                .WithUser(user)
                .WithFutureDates(7)
                .BuildReserved();
        }

        /// <summary>
        /// Creates a booking that can be cancelled (future dates, confirmed)
        /// </summary>
        public static Booking CancellableBooking()
        {
            return BookingTestDataBuilder.Create()
                .WithFutureDates(15)
                .BuildConfirmed();
        }

        /// <summary>
        /// Creates a booking that cannot be cancelled (already started)
        /// </summary>
        public static Booking NonCancellableBooking()
        {
            return BookingTestDataBuilder.Create()
                .WithStartedDates()
                .BuildConfirmed();
        }

        /// <summary>
        /// Creates a completed booking scenario
        /// </summary>
        public static Booking CompletedBooking()
        {
            return BookingTestDataBuilder.Create()
                .WithPastDates(30)
                .BuildCompleted();
        }

        /// <summary>
        /// Creates multiple bookings for the same apartment (overlap testing)
        /// </summary>
        public static (Booking first, Booking overlapping) OverlappingBookings()
        {
            var apartment = ApartmentTestDataBuilder.CreateDefault();
            var user1 = UserTestDataBuilder.CreateWithEmail("<EMAIL>");
            var user2 = UserTestDataBuilder.CreateWithEmail("<EMAIL>");
            
            var startDate = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(10));
            
            var firstBooking = BookingTestDataBuilder.Create()
                .WithApartment(apartment)
                .WithUser(user1)
                .WithDuration(startDate, startDate.AddDays(5))
                .BuildConfirmed();
                
            var overlappingBooking = BookingTestDataBuilder.Create()
                .WithApartment(apartment)
                .WithUser(user2)
                .WithDuration(startDate.AddDays(3), startDate.AddDays(8)) // Overlaps with first
                .BuildReserved();
                
            return (firstBooking, overlappingBooking);
        }
    }

    /// <summary>
    /// Common test scenarios for users
    /// </summary>
    public static class Users
    {
        /// <summary>
        /// Creates a typical guest user
        /// </summary>
        public static User Guest()
        {
            return UserTestDataBuilder.Create()
                .WithFirstName("John")
                .WithLastName("Guest")
                .WithEmail("<EMAIL>")
                .Build();
        }

        /// <summary>
        /// Creates a frequent traveler user
        /// </summary>
        public static User FrequentTraveler()
        {
            return UserTestDataBuilder.Create()
                .WithFirstName("Sarah")
                .WithLastName("Traveler")
                .WithEmail("<EMAIL>")
                .Build();
        }

        /// <summary>
        /// Creates a business traveler user
        /// </summary>
        public static User BusinessTraveler()
        {
            return UserTestDataBuilder.Create()
                .WithFirstName("Michael")
                .WithLastName("Business")
                .WithEmail("<EMAIL>")
                .Build();
        }

        /// <summary>
        /// Creates users for cross-user access testing
        /// </summary>
        public static (User owner, User otherUser) OwnerAndOtherUser()
        {
            var owner = UserTestDataBuilder.Create()
                .WithFirstName("Resource")
                .WithLastName("Owner")
                .WithEmail("<EMAIL>")
                .Build();
                
            var otherUser = UserTestDataBuilder.Create()
                .WithFirstName("Other")
                .WithLastName("User")
                .WithEmail("<EMAIL>")
                .Build();
                
            return (owner, otherUser);
        }
    }

    /// <summary>
    /// Common test scenarios for apartments
    /// </summary>
    public static class Apartments
    {
        /// <summary>
        /// Creates a city center apartment
        /// </summary>
        public static Apartment CityCenterApartment()
        {
            return ApartmentTestDataBuilder.Create()
                .WithName("City Center Loft")
                .WithDescription("Modern loft in the heart of the city")
                .WithAddress("United States", "New York", "10001", "New York", "123 Broadway")
                .WithPrice(200.0m)
                .WithCleaningFee(100.0m)
                .WithAmenities(Amenity.WiFi, Amenity.AirConditioning, Amenity.Parking)
                .Build();
        }

        /// <summary>
        /// Creates a beachfront apartment
        /// </summary>
        public static Apartment BeachfrontApartment()
        {
            return ApartmentTestDataBuilder.Create()
                .WithName("Beachfront Paradise")
                .WithDescription("Stunning oceanfront apartment with panoramic views")
                .WithAddress("United States", "California", "90210", "Malibu", "456 Ocean Drive")
                .WithPrice(350.0m)
                .WithCleaningFee(150.0m)
                .WithAmenities(Amenity.WiFi, Amenity.SwimmingPoool, Amenity.Terrace, Amenity.GardenView)
                .Build();
        }

        /// <summary>
        /// Creates a mountain cabin
        /// </summary>
        public static Apartment MountainCabin()
        {
            return ApartmentTestDataBuilder.Create()
                .WithName("Mountain Retreat")
                .WithDescription("Cozy cabin with breathtaking mountain views")
                .WithAddress("United States", "Colorado", "80424", "Breckenridge", "789 Mountain Trail")
                .WithPrice(120.0m)
                .WithCleaningFee(60.0m)
                .WithAmenities(Amenity.WiFi, Amenity.MountainView, Amenity.PetFriendly)
                .Build();
        }

        /// <summary>
        /// Creates apartments in different price ranges
        /// </summary>
        public static (Apartment budget, Apartment mid, Apartment luxury) DifferentPriceRanges()
        {
            var budget = ApartmentTestDataBuilder.CreateBudget();
            
            var mid = ApartmentTestDataBuilder.Create()
                .WithName("Mid-Range Apartment")
                .WithPrice(100.0m)
                .WithCleaningFee(50.0m)
                .WithBasicAmenities()
                .Build();
                
            var luxury = ApartmentTestDataBuilder.CreateLuxury();
            
            return (budget, mid, luxury);
        }
    }

    /// <summary>
    /// Common test scenarios for value objects
    /// </summary>
    public static class ValueObjects
    {
        /// <summary>
        /// Creates common date ranges for testing
        /// </summary>
        public static class DateRanges
        {
            public static DateRange NextWeek()
            {
                var start = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(7));
                return DateRange.Create(start, start.AddDays(6));
            }

            public static DateRange NextMonth()
            {
                var start = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(30));
                return DateRange.Create(start, start.AddDays(29));
            }

            public static DateRange Weekend()
            {
                var friday = GetNextFriday();
                return DateRange.Create(friday, friday.AddDays(2));
            }

            public static DateRange SingleDay()
            {
                var date = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(14));
                return DateRange.Create(date, date);
            }
        }

        /// <summary>
        /// Creates common money amounts for testing
        /// </summary>
        public static class Money
        {
            public static Domain.Shared.Money OneHundredUsd() => new(100.0m, Currency.Usd);
            public static Domain.Shared.Money FiftyEur() => new(50.0m, Currency.Eur);
            public static Domain.Shared.Money Zero() => Domain.Shared.Money.Zero();
            public static Domain.Shared.Money LuxuryPrice() => new(500.0m, Currency.Usd);
            public static Domain.Shared.Money BudgetPrice() => new(25.0m, Currency.Usd);
        }
    }

    /// <summary>
    /// Helper method to get the next Friday from today
    /// </summary>
    private static DateOnly GetNextFriday()
    {
        var today = DateOnly.FromDateTime(DateTime.UtcNow);
        var daysUntilFriday = ((int)DayOfWeek.Friday - (int)today.DayOfWeek + 7) % 7;
        if (daysUntilFriday == 0) daysUntilFriday = 7; // If today is Friday, get next Friday
        return today.AddDays(daysUntilFriday);
    }
}
