using Bookify.Domain.Entities.Abstractions;
using Bookify.Domain.Entities.Bookings.ValueObjects;
using Bookify.Domain.Shared;
using System.Reflection;

namespace Bookify.Domain.UnitTests.TestUtilities;

/// <summary>
/// Helper methods for testing domain entities and value objects
/// </summary>
public static class TestHelpers
{
    /// <summary>
    /// Date and time utilities for testing
    /// </summary>
    public static class DateTimeHelpers
    {
        public static readonly DateTime BaseTestDateTime = new(2024, 1, 1, 12, 0, 0, DateTimeKind.Utc);
        
        public static DateTime UtcNow => BaseTestDateTime;
        public static DateTime Tomorrow => BaseTestDateTime.AddDays(1);
        public static DateTime NextWeek => BaseTestDateTime.AddDays(7);
        public static DateTime NextMonth => BaseTestDateTime.AddMonths(1);
        public static DateTime LastWeek => BaseTestDateTime.AddDays(-7);
        public static DateTime LastMonth => BaseTestDateTime.AddMonths(-1);

        public static DateOnly TodayOnly => DateOnly.FromDateTime(BaseTestDateTime);
        public static DateOnly TomorrowOnly => DateOnly.FromDateTime(Tomorrow);
        public static DateOnly NextWeekOnly => DateOnly.FromDateTime(NextWeek);
        public static DateOnly LastWeekOnly => DateOnly.FromDateTime(LastWeek);

        /// <summary>
        /// Creates a date range starting from a specific number of days from the base test date
        /// </summary>
        public static DateRange CreateDateRange(int startDaysFromNow, int durationDays)
        {
            var startDate = DateOnly.FromDateTime(BaseTestDateTime.AddDays(startDaysFromNow));
            var endDate = startDate.AddDays(durationDays - 1);
            return DateRange.Create(startDate, endDate);
        }

        /// <summary>
        /// Creates a date range for testing overlaps
        /// </summary>
        public static (DateRange first, DateRange overlapping, DateRange nonOverlapping) CreateOverlapTestRanges()
        {
            var baseStart = TodayOnly.AddDays(10);
            
            var first = DateRange.Create(baseStart, baseStart.AddDays(5));
            var overlapping = DateRange.Create(baseStart.AddDays(3), baseStart.AddDays(8));
            var nonOverlapping = DateRange.Create(baseStart.AddDays(10), baseStart.AddDays(15));
            
            return (first, overlapping, nonOverlapping);
        }
    }

    /// <summary>
    /// Money utilities for testing
    /// </summary>
    public static class MoneyHelpers
    {
        public static Money CreateMoney(decimal amount, Currency currency = Currency.Usd) => new(amount, currency);
        
        public static Money[] CreateMoneyArray(params (decimal amount, Currency currency)[] values)
        {
            return values.Select(v => new Money(v.amount, v.currency)).ToArray();
        }

        public static Money RandomMoney(Currency currency = Currency.Usd, decimal min = 1, decimal max = 1000)
        {
            var random = new Random();
            var amount = (decimal)(random.NextDouble() * (double)(max - min) + (double)min);
            return new Money(Math.Round(amount, 2), currency);
        }
    }

    /// <summary>
    /// Domain event testing utilities
    /// </summary>
    public static class DomainEventHelpers
    {
        /// <summary>
        /// Clears all domain events from an entity
        /// </summary>
        public static void ClearDomainEvents(IEntity entity)
        {
            entity.ClearDomainEvents();
        }

        /// <summary>
        /// Gets all domain events of a specific type from an entity
        /// </summary>
        public static IEnumerable<T> GetDomainEventsOfType<T>(IEntity entity) where T : IDomainEvent
        {
            return entity.GetDomainEvents().OfType<T>();
        }

        /// <summary>
        /// Asserts that an entity has published exactly one domain event of the specified type
        /// </summary>
        public static T AssertSingleDomainEvent<T>(IEntity entity) where T : IDomainEvent
        {
            var events = GetDomainEventsOfType<T>(entity).ToList();
            
            if (events.Count == 0)
                throw new InvalidOperationException($"No domain event of type {typeof(T).Name} was found");
            
            if (events.Count > 1)
                throw new InvalidOperationException($"Expected exactly one domain event of type {typeof(T).Name}, but found {events.Count}");
            
            return events.Single();
        }

        /// <summary>
        /// Asserts that an entity has published the expected number of domain events
        /// </summary>
        public static void AssertDomainEventCount(IEntity entity, int expectedCount)
        {
            var actualCount = entity.GetDomainEvents().Count();
            if (actualCount != expectedCount)
                throw new InvalidOperationException($"Expected {expectedCount} domain events, but found {actualCount}");
        }
    }

    /// <summary>
    /// Reflection utilities for testing private/internal members
    /// </summary>
    public static class ReflectionHelpers
    {
        /// <summary>
        /// Sets a private property value using reflection
        /// </summary>
        public static void SetPrivateProperty<T>(T obj, string propertyName, object value)
        {
            var property = typeof(T).GetProperty(propertyName, BindingFlags.NonPublic | BindingFlags.Public | BindingFlags.Instance);
            if (property == null)
                throw new ArgumentException($"Property '{propertyName}' not found on type '{typeof(T).Name}'");
            
            property.SetValue(obj, value);
        }

        /// <summary>
        /// Gets a private property value using reflection
        /// </summary>
        public static TValue GetPrivateProperty<T, TValue>(T obj, string propertyName)
        {
            var property = typeof(T).GetProperty(propertyName, BindingFlags.NonPublic | BindingFlags.Public | BindingFlags.Instance);
            if (property == null)
                throw new ArgumentException($"Property '{propertyName}' not found on type '{typeof(T).Name}'");
            
            return (TValue)property.GetValue(obj)!;
        }

        /// <summary>
        /// Invokes a private method using reflection
        /// </summary>
        public static TResult InvokePrivateMethod<T, TResult>(T obj, string methodName, params object[] parameters)
        {
            var method = typeof(T).GetMethod(methodName, BindingFlags.NonPublic | BindingFlags.Instance);
            if (method == null)
                throw new ArgumentException($"Method '{methodName}' not found on type '{typeof(T).Name}'");
            
            return (TResult)method.Invoke(obj, parameters)!;
        }
    }

    /// <summary>
    /// Collection utilities for testing
    /// </summary>
    public static class CollectionHelpers
    {
        /// <summary>
        /// Creates a list with the specified items
        /// </summary>
        public static List<T> CreateList<T>(params T[] items) => new(items);

        /// <summary>
        /// Creates a list of unique items using a factory function
        /// </summary>
        public static List<T> CreateUniqueList<T>(int count, Func<int, T> factory)
        {
            return Enumerable.Range(0, count).Select(factory).ToList();
        }

        /// <summary>
        /// Shuffles a collection randomly
        /// </summary>
        public static IEnumerable<T> Shuffle<T>(IEnumerable<T> source)
        {
            var random = new Random();
            return source.OrderBy(x => random.Next());
        }
    }

    /// <summary>
    /// String utilities for testing
    /// </summary>
    public static class StringHelpers
    {
        /// <summary>
        /// Generates a random string of specified length
        /// </summary>
        public static string RandomString(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            var random = new Random();
            return new string(Enumerable.Repeat(chars, length)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }

        /// <summary>
        /// Generates a random email address
        /// </summary>
        public static string RandomEmail()
        {
            var username = RandomString(8).ToLower();
            var domain = RandomString(6).ToLower();
            return $"{username}@{domain}.com";
        }

        /// <summary>
        /// Creates a string that exceeds the maximum length
        /// </summary>
        public static string CreateLongString(int length) => new('A', length);
    }

    /// <summary>
    /// Validation utilities for testing
    /// </summary>
    public static class ValidationHelpers
    {
        /// <summary>
        /// Tests that an action throws an ArgumentException
        /// </summary>
        public static void ShouldThrowArgumentException(Action action, string expectedParamName = null)
        {
            try
            {
                action();
                throw new InvalidOperationException("Expected ArgumentException was not thrown");
            }
            catch (ArgumentException ex)
            {
                if (expectedParamName != null && ex.ParamName != expectedParamName)
                    throw new InvalidOperationException($"Expected parameter name '{expectedParamName}', but got '{ex.ParamName}'");
            }
        }

        /// <summary>
        /// Tests that an action throws a specific exception type
        /// </summary>
        public static void ShouldThrow<TException>(Action action) where TException : Exception
        {
            try
            {
                action();
                throw new InvalidOperationException($"Expected {typeof(TException).Name} was not thrown");
            }
            catch (TException)
            {
                // Expected exception was thrown
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Expected {typeof(TException).Name}, but got {ex.GetType().Name}");
            }
        }
    }

    /// <summary>
    /// Performance testing utilities
    /// </summary>
    public static class PerformanceHelpers
    {
        /// <summary>
        /// Measures the execution time of an action
        /// </summary>
        public static TimeSpan MeasureExecutionTime(Action action)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            action();
            stopwatch.Stop();
            return stopwatch.Elapsed;
        }

        /// <summary>
        /// Executes an action multiple times and returns the average execution time
        /// </summary>
        public static TimeSpan MeasureAverageExecutionTime(Action action, int iterations = 100)
        {
            var totalTime = TimeSpan.Zero;
            
            for (int i = 0; i < iterations; i++)
            {
                totalTime += MeasureExecutionTime(action);
            }
            
            return TimeSpan.FromTicks(totalTime.Ticks / iterations);
        }
    }
}
