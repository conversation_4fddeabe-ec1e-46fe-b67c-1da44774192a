using Bookify.Domain.Entities.Abstractions;
using Bookify.Domain.Entities.Bookings;
using Bookify.Domain.Entities.Bookings.ValueObjects;
using Bookify.Domain.Entities.Users;
using Bookify.Domain.Shared;
using FluentAssertions;
using FluentAssertions.Execution;
using FluentAssertions.Primitives;

namespace Bookify.Domain.UnitTests.TestUtilities;

/// <summary>
/// Custom assertions for domain entities and value objects
/// </summary>
public static class CustomAssertions
{
    /// <summary>
    /// Asserts that a domain event of the specified type was published by the entity
    /// </summary>
    public static T ShouldHavePublishedDomainEvent<T>(this IEntity entity) where T : IDomainEvent
    {
        var domainEvent = entity.GetDomainEvents().OfType<T>().SingleOrDefault();

        if (domainEvent == null)
        {
            throw new AssertionFailedException($"Expected domain event of type {typeof(T).Name} to be published, but it was not found.");
        }

        return domainEvent;
    }

    /// <summary>
    /// Asserts that no domain events were published by the entity
    /// </summary>
    public static void ShouldNotHavePublishedAnyDomainEvents(this IEntity entity)
    {
        entity.GetDomainEvents().Should().BeEmpty("because no domain events should have been published");
    }

    /// <summary>
    /// Asserts that exactly the specified number of domain events were published
    /// </summary>
    public static void ShouldHavePublishedDomainEvents(this IEntity entity, int expectedCount)
    {
        entity.GetDomainEvents().Should().HaveCount(expectedCount, 
            $"because exactly {expectedCount} domain event(s) should have been published");
    }

    /// <summary>
    /// Asserts that a booking has the expected status
    /// </summary>
    public static BookingAssertions Should(this Booking booking)
    {
        return new BookingAssertions(booking);
    }

    /// <summary>
    /// Asserts that a user has the expected properties
    /// </summary>
    public static UserAssertions Should(this User user)
    {
        return new UserAssertions(user);
    }

    /// <summary>
    /// Asserts that a Result is successful
    /// </summary>
    public static void ShouldBeSuccess(this Result result)
    {
        result.IsSuccess.Should().BeTrue("because the operation should succeed");
        result.IsFailure.Should().BeFalse("because the operation should succeed");
    }

    /// <summary>
    /// Asserts that a Result is successful and returns the expected value
    /// </summary>
    public static void ShouldBeSuccess<T>(this Result<T> result, T expectedValue)
    {
        result.IsSuccess.Should().BeTrue("because the operation should succeed");
        result.IsFailure.Should().BeFalse("because the operation should succeed");
        result.Value.Should().Be(expectedValue);
    }

    /// <summary>
    /// Asserts that a Result is a failure with the expected error
    /// </summary>
    public static void ShouldBeFailure(this Result result, Error expectedError)
    {
        result.IsFailure.Should().BeTrue("because the operation should fail");
        result.IsSuccess.Should().BeFalse("because the operation should fail");
        result.Error.Should().Be(expectedError);
    }

    /// <summary>
    /// Asserts that a Result is a failure with the expected error
    /// </summary>
    public static void ShouldBeFailure<T>(this Result<T> result, Error expectedError)
    {
        result.IsFailure.Should().BeTrue("because the operation should fail");
        result.IsSuccess.Should().BeFalse("because the operation should fail");
        result.Error.Should().Be(expectedError);
    }

    /// <summary>
    /// Asserts that a Money object has the expected amount and currency
    /// </summary>
    public static void ShouldBe(this Money money, decimal expectedAmount, Currency expectedCurrency)
    {
        using (new AssertionScope())
        {
            money.Amount.Should().Be(expectedAmount);
            money.Currency.Should().Be(expectedCurrency);
        }
    }

    /// <summary>
    /// Asserts that a DateRange has the expected start and end dates
    /// </summary>
    public static void ShouldBe(this DateRange dateRange, DateOnly expectedStart, DateOnly expectedEnd)
    {
        using (new AssertionScope())
        {
            dateRange.Start.Should().Be(expectedStart);
            dateRange.End.Should().Be(expectedEnd);
        }
    }
}

/// <summary>
/// Custom assertions for Booking entities
/// </summary>
public class BookingAssertions : ReferenceTypeAssertions<Booking, BookingAssertions>
{
    public BookingAssertions(Booking booking) : base(booking)
    {
    }

    protected override string Identifier => "booking";

    public AndConstraint<BookingAssertions> BeInStatus(BookingStatus expectedStatus, string because = "", params object[] becauseArgs)
    {
        Execute.Assertion
            .ForCondition(Subject.Status == expectedStatus)
            .BecauseOf(because, becauseArgs)
            .FailWith("Expected booking to be in status {0}{reason}, but found {1}.", expectedStatus, Subject.Status);

        return new AndConstraint<BookingAssertions>(this);
    }

    public AndConstraint<BookingAssertions> HaveTotalPrice(Money expectedTotalPrice, string because = "", params object[] becauseArgs)
    {
        Execute.Assertion
            .ForCondition(Subject.TotalPrice.Equals(expectedTotalPrice))
            .BecauseOf(because, becauseArgs)
            .FailWith("Expected booking to have total price {0}{reason}, but found {1}.", expectedTotalPrice, Subject.TotalPrice);

        return new AndConstraint<BookingAssertions>(this);
    }

    public AndConstraint<BookingAssertions> BelongToUser(UserId expectedUserId, string because = "", params object[] becauseArgs)
    {
        Execute.Assertion
            .ForCondition(Subject.UserId.Equals(expectedUserId))
            .BecauseOf(because, becauseArgs)
            .FailWith("Expected booking to belong to user {0}{reason}, but found {1}.", expectedUserId, Subject.UserId);

        return new AndConstraint<BookingAssertions>(this);
    }

    public AndConstraint<BookingAssertions> HaveDuration(DateRange expectedDuration, string because = "", params object[] becauseArgs)
    {
        Execute.Assertion
            .ForCondition(Subject.Duration.Equals(expectedDuration))
            .BecauseOf(because, becauseArgs)
            .FailWith("Expected booking to have duration {0}{reason}, but found {1}.", expectedDuration, Subject.Duration);

        return new AndConstraint<BookingAssertions>(this);
    }

    public AndConstraint<BookingAssertions> BeConfirmedOn(DateTime expectedConfirmedOnUtc, string because = "", params object[] becauseArgs)
    {
        Execute.Assertion
            .ForCondition(Subject.ConfirmedOnUtc == expectedConfirmedOnUtc)
            .BecauseOf(because, becauseArgs)
            .FailWith("Expected booking to be confirmed on {0}{reason}, but found {1}.", expectedConfirmedOnUtc, Subject.ConfirmedOnUtc);

        return new AndConstraint<BookingAssertions>(this);
    }
}

/// <summary>
/// Custom assertions for User entities
/// </summary>
public class UserAssertions : ReferenceTypeAssertions<User, UserAssertions>
{
    public UserAssertions(User user) : base(user)
    {
    }

    protected override string Identifier => "user";

    public AndConstraint<UserAssertions> HaveRole(Role expectedRole, string because = "", params object[] becauseArgs)
    {
        Execute.Assertion
            .ForCondition(Subject.Roles.Contains(expectedRole))
            .BecauseOf(because, becauseArgs)
            .FailWith("Expected user to have role {0}{reason}, but it was not found in roles: {1}.", 
                expectedRole, string.Join(", ", Subject.Roles.Select(r => r.Name)));

        return new AndConstraint<UserAssertions>(this);
    }

    public AndConstraint<UserAssertions> HaveEmail(string expectedEmail, string because = "", params object[] becauseArgs)
    {
        Execute.Assertion
            .ForCondition(Subject.Email.Value.Equals(expectedEmail, StringComparison.OrdinalIgnoreCase))
            .BecauseOf(because, becauseArgs)
            .FailWith("Expected user to have email {0}{reason}, but found {1}.", expectedEmail, Subject.Email.Value);

        return new AndConstraint<UserAssertions>(this);
    }

    public AndConstraint<UserAssertions> HaveFullName(string expectedFirstName, string expectedLastName, string because = "", params object[] becauseArgs)
    {
        using (new AssertionScope())
        {
            Execute.Assertion
                .ForCondition(Subject.FirstName == expectedFirstName)
                .BecauseOf(because, becauseArgs)
                .FailWith("Expected user to have first name {0}{reason}, but found {1}.", expectedFirstName, Subject.FirstName);

            Execute.Assertion
                .ForCondition(Subject.LastName == expectedLastName)
                .BecauseOf(because, becauseArgs)
                .FailWith("Expected user to have last name {0}{reason}, but found {1}.", expectedLastName, Subject.LastName);
        }

        return new AndConstraint<UserAssertions>(this);
    }
}
