﻿using Bookify.Domain.Entities.Bookings;
using Bookify.Domain.Entities.Bookings.Events;
using Bookify.Domain.Entities.Bookings.ValueObjects;
using Bookify.Domain.Entities.Users;
using Bookify.Domain.Shared;
using Bookify.Domain.UnitTests.Apartments;
using Bookify.Domain.UnitTests.Users;
using Bookify.Domain.UnitTests.TestBuilders;
using FluentAssertions;

namespace Bookify.Domain.UnitTests.Bookings;
public class BookingTests : BaseTest
{
    private static readonly DateTime UtcNow = new(2024, 1, 1, 12, 0, 0, DateTimeKind.Utc);
    private static readonly DateOnly StartDate = new(2024, 2, 1);
    private static readonly DateOnly EndDate = new(2024, 2, 10);
    private static readonly Money BasePrice = new(100.0m, Currency.Usd);
    private static readonly Money CleaningFee = new(50.0m, Currency.Usd);

    [Fact]
    public void Reserve_Should_RaiseBookingReservedDomainEvent()
    {
        // Arrange
        var user = User.Create(UserData.FirstName, UserData.LastName, UserData.Email);
        var period = DateRange.Create(StartDate, EndDate);
        var apartment = ApartmentData.Create(BasePrice, CleaningFee);
        var pricingService = new PricingService();

        // Act
        var booking = Booking.Reserve(apartment, user.Id, period, UtcNow, pricingService);

        // Assert
        var domainEvent = AssertDomainEventWasPublished<BookingReservedDomainEvent>(booking);
        domainEvent.BookingId.Should().Be(booking.Id);
    }

    [Fact]
    public void Reserve_Should_SetCorrectBookingProperties()
    {
        // Arrange
        var user = User.Create(UserData.FirstName, UserData.LastName, UserData.Email);
        var period = DateRange.Create(StartDate, EndDate);
        var apartment = ApartmentData.Create(BasePrice, CleaningFee);
        var pricingService = new PricingService();

        // Act
        var booking = Booking.Reserve(apartment, user.Id, period, UtcNow, pricingService);

        // Assert
        booking.ApartmentId.Should().Be(apartment.Id);
        booking.UserId.Should().Be(user.Id);
        booking.Duration.Should().Be(period);
        booking.Status.Should().Be(BookingStatus.Reserved);
        booking.CreatedOnUtc.Should().Be(UtcNow);
        booking.ConfirmedOnUtc.Should().BeNull();
        booking.RejectedOnUtc.Should().BeNull();
        booking.CompletedOnUtc.Should().BeNull();
        booking.CancelledOnUtc.Should().BeNull();
    }

    [Fact]
    public void Reserve_Should_CalculateCorrectPricing()
    {
        // Arrange
        var user = User.Create(UserData.FirstName, UserData.LastName, UserData.Email);
        var period = DateRange.Create(StartDate, EndDate);
        var apartment = ApartmentData.Create(BasePrice, CleaningFee);
        var pricingService = new PricingService();
        var expectedPriceForPeriod = new Money(BasePrice.Amount * period.LengthInDays, BasePrice.Currency);
        var expectedTotalPrice = new Money(expectedPriceForPeriod.Amount + CleaningFee.Amount, BasePrice.Currency);

        // Act
        var booking = Booking.Reserve(apartment, user.Id, period, UtcNow, pricingService);

        // Assert
        booking.PriceForPeriod.Should().Be(expectedPriceForPeriod);
        booking.CleaningFee.Should().Be(CleaningFee);
        booking.AmenitiesUpCharge.Should().Be(Money.Zero());
        booking.TotalPrice.Should().Be(expectedTotalPrice);
    }

    [Fact]
    public void Reserve_Should_UpdateApartmentLastBookedDate()
    {
        // Arrange
        var user = User.Create(UserData.FirstName, UserData.LastName, UserData.Email);
        var period = DateRange.Create(StartDate, EndDate);
        var apartment = ApartmentData.Create(BasePrice, CleaningFee);
        var pricingService = new PricingService();

        // Act
        var booking = Booking.Reserve(apartment, user.Id, period, UtcNow, pricingService);

        // Assert
        apartment.LastBookedOnUtc.Should().Be(UtcNow);
    }

    [Fact]
    public void Confirm_Should_ReturnSuccess_WhenBookingIsReserved()
    {
        // Arrange
        var user = User.Create(UserData.FirstName, UserData.LastName, UserData.Email);
        var period = DateRange.Create(StartDate, EndDate);
        var apartment = ApartmentData.Create(BasePrice);
        var pricingService = new PricingService();
        var booking = Booking.Reserve(apartment, user.Id, period, UtcNow, pricingService);
        var confirmationTime = UtcNow.AddHours(1);

        // Act
        var result = booking.Confirm(confirmationTime);

        // Assert
        result.IsSuccess.Should().BeTrue();
        booking.Status.Should().Be(BookingStatus.Confirmed);
        booking.ConfirmedOnUtc.Should().Be(confirmationTime);
    }

    [Fact]
    public void Confirm_Should_RaiseBookingConfirmedDomainEvent()
    {
        // Arrange
        var user = User.Create(UserData.FirstName, UserData.LastName, UserData.Email);
        var period = DateRange.Create(StartDate, EndDate);
        var apartment = ApartmentData.Create(BasePrice);
        var pricingService = new PricingService();
        var booking = Booking.Reserve(apartment, user.Id, period, UtcNow, pricingService);
        booking.ClearDomainEvents(); // Clear the reservation event

        // Act
        var result = booking.Confirm(UtcNow.AddHours(1));

        // Assert
        result.IsSuccess.Should().BeTrue();
        var domainEvent = AssertDomainEventWasPublished<BookingConfirmedDomainEvent>(booking);
        domainEvent.BookingId.Should().Be(booking.Id);
    }

    [Fact]
    public void Confirm_Should_ReturnFailure_WhenBookingIsNotReserved()
    {
        // Arrange
        var user = User.Create(UserData.FirstName, UserData.LastName, UserData.Email);
        var period = DateRange.Create(StartDate, EndDate);
        var apartment = ApartmentData.Create(BasePrice);
        var pricingService = new PricingService();
        var booking = Booking.Reserve(apartment, user.Id, period, UtcNow, pricingService);

        // First confirm the booking
        booking.Confirm(UtcNow.AddHours(1));

        // Act - Try to confirm again
        var result = booking.Confirm(UtcNow.AddHours(2));

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(BookingErrors.NotReserved);
    }

    [Fact]
    public void Reject_Should_ReturnSuccess_WhenBookingIsReserved()
    {
        // Arrange
        var user = User.Create(UserData.FirstName, UserData.LastName, UserData.Email);
        var period = DateRange.Create(StartDate, EndDate);
        var apartment = ApartmentData.Create(BasePrice);
        var pricingService = new PricingService();
        var booking = Booking.Reserve(apartment, user.Id, period, UtcNow, pricingService);
        var rejectionTime = UtcNow.AddHours(1);

        // Act
        var result = booking.Reject(rejectionTime);

        // Assert
        result.IsSuccess.Should().BeTrue();
        booking.Status.Should().Be(BookingStatus.Rejected);
        booking.RejectedOnUtc.Should().Be(rejectionTime);
    }

    [Fact]
    public void Reject_Should_RaiseBookingRejectedDomainEvent()
    {
        // Arrange
        var user = User.Create(UserData.FirstName, UserData.LastName, UserData.Email);
        var period = DateRange.Create(StartDate, EndDate);
        var apartment = ApartmentData.Create(BasePrice);
        var pricingService = new PricingService();
        var booking = Booking.Reserve(apartment, user.Id, period, UtcNow, pricingService);
        booking.ClearDomainEvents(); // Clear the reservation event

        // Act
        var result = booking.Reject(UtcNow.AddHours(1));

        // Assert
        result.IsSuccess.Should().BeTrue();
        var domainEvent = AssertDomainEventWasPublished<BookingRejectedDomainEvent>(booking);
        domainEvent.BookingId.Should().Be(booking.Id);
    }

    [Fact]
    public void Reject_Should_ReturnFailure_WhenBookingIsNotReserved()
    {
        // Arrange
        var user = User.Create(UserData.FirstName, UserData.LastName, UserData.Email);
        var period = DateRange.Create(StartDate, EndDate);
        var apartment = ApartmentData.Create(BasePrice);
        var pricingService = new PricingService();
        var booking = Booking.Reserve(apartment, user.Id, period, UtcNow, pricingService);

        // First confirm the booking
        booking.Confirm(UtcNow.AddHours(1));

        // Act - Try to reject a confirmed booking
        var result = booking.Reject(UtcNow.AddHours(2));

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(BookingErrors.NotReserved);
    }

    [Fact]
    public void Complete_Should_ReturnSuccess_WhenBookingIsConfirmed()
    {
        // Arrange
        var user = User.Create(UserData.FirstName, UserData.LastName, UserData.Email);
        var period = DateRange.Create(StartDate, EndDate);
        var apartment = ApartmentData.Create(BasePrice);
        var pricingService = new PricingService();
        var booking = Booking.Reserve(apartment, user.Id, period, UtcNow, pricingService);
        booking.Confirm(UtcNow.AddHours(1));
        var completionTime = UtcNow.AddDays(15);

        // Act
        var result = booking.Complete(completionTime);

        // Assert
        result.IsSuccess.Should().BeTrue();
        booking.Status.Should().Be(BookingStatus.Completed);
        booking.CompletedOnUtc.Should().Be(completionTime);
    }

    [Fact]
    public void Complete_Should_RaiseBookingCompletedDomainEvent()
    {
        // Arrange
        var user = User.Create(UserData.FirstName, UserData.LastName, UserData.Email);
        var period = DateRange.Create(StartDate, EndDate);
        var apartment = ApartmentData.Create(BasePrice);
        var pricingService = new PricingService();
        var booking = Booking.Reserve(apartment, user.Id, period, UtcNow, pricingService);
        booking.Confirm(UtcNow.AddHours(1));
        booking.ClearDomainEvents(); // Clear previous events

        // Act
        var result = booking.Complete(UtcNow.AddDays(15));

        // Assert
        result.IsSuccess.Should().BeTrue();
        var domainEvent = AssertDomainEventWasPublished<BookingCompletedDomainEvent>(booking);
        domainEvent.BookingId.Should().Be(booking.Id);
    }

    [Fact]
    public void Complete_Should_ReturnFailure_WhenBookingIsNotConfirmed()
    {
        // Arrange
        var user = User.Create(UserData.FirstName, UserData.LastName, UserData.Email);
        var period = DateRange.Create(StartDate, EndDate);
        var apartment = ApartmentData.Create(BasePrice);
        var pricingService = new PricingService();
        var booking = Booking.Reserve(apartment, user.Id, period, UtcNow, pricingService);

        // Act - Try to complete without confirming
        var result = booking.Complete(UtcNow.AddDays(15));

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(BookingErrors.NotConfirmed);
    }

    [Fact]
    public void Cancel_Should_ReturnSuccess_WhenBookingIsConfirmedAndNotStarted()
    {
        // Arrange
        var futureStartDate = new DateOnly(2024, 3, 1);
        var futureEndDate = new DateOnly(2024, 3, 10);
        var user = User.Create(UserData.FirstName, UserData.LastName, UserData.Email);
        var period = DateRange.Create(futureStartDate, futureEndDate);
        var apartment = ApartmentData.Create(BasePrice);
        var pricingService = new PricingService();
        var booking = Booking.Reserve(apartment, user.Id, period, UtcNow, pricingService);
        booking.Confirm(UtcNow.AddHours(1));
        var cancellationTime = UtcNow.AddDays(10); // Before the booking starts

        // Act
        var result = booking.Cancel(cancellationTime);

        // Assert
        result.IsSuccess.Should().BeTrue();
        booking.Status.Should().Be(BookingStatus.Cancelled);
        booking.CompletedOnUtc.Should().Be(cancellationTime); // Note: Cancel sets CompletedOnUtc
    }

    [Fact]
    public void Cancel_Should_RaiseBookingCancelledDomainEvent()
    {
        // Arrange
        var futureStartDate = new DateOnly(2024, 3, 1);
        var futureEndDate = new DateOnly(2024, 3, 10);
        var user = User.Create(UserData.FirstName, UserData.LastName, UserData.Email);
        var period = DateRange.Create(futureStartDate, futureEndDate);
        var apartment = ApartmentData.Create(BasePrice);
        var pricingService = new PricingService();
        var booking = Booking.Reserve(apartment, user.Id, period, UtcNow, pricingService);
        booking.Confirm(UtcNow.AddHours(1));
        booking.ClearDomainEvents(); // Clear previous events

        // Act
        var result = booking.Cancel(UtcNow.AddDays(10));

        // Assert
        result.IsSuccess.Should().BeTrue();
        var domainEvent = AssertDomainEventWasPublished<BookingCancelledDomainEvent>(booking);
        domainEvent.BookingId.Should().Be(booking.Id);
    }

    [Fact]
    public void Cancel_Should_ReturnFailure_WhenBookingIsNotConfirmed()
    {
        // Arrange
        var user = User.Create(UserData.FirstName, UserData.LastName, UserData.Email);
        var period = DateRange.Create(StartDate, EndDate);
        var apartment = ApartmentData.Create(BasePrice);
        var pricingService = new PricingService();
        var booking = Booking.Reserve(apartment, user.Id, period, UtcNow, pricingService);

        // Act - Try to cancel without confirming
        var result = booking.Cancel(UtcNow.AddDays(10));

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(BookingErrors.NotConfirmed);
    }

    [Fact]
    public void Cancel_Should_ReturnFailure_WhenBookingHasAlreadyStarted()
    {
        // Arrange
        var user = User.Create(UserData.FirstName, UserData.LastName, UserData.Email);
        var period = DateRange.Create(StartDate, EndDate);
        var apartment = ApartmentData.Create(BasePrice);
        var pricingService = new PricingService();
        var booking = Booking.Reserve(apartment, user.Id, period, UtcNow, pricingService);
        booking.Confirm(UtcNow.AddHours(1));
        var cancellationTime = StartDate.AddDays(1).ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc); // After booking started

        // Act
        var result = booking.Cancel(cancellationTime);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(BookingErrors.AlreadyStarted);
    }

    [Theory]
    [InlineData(BookingStatus.Reserved)]
    [InlineData(BookingStatus.Confirmed)]
    [InlineData(BookingStatus.Rejected)]
    [InlineData(BookingStatus.Completed)]
    [InlineData(BookingStatus.Cancelled)]
    public void BookingStatus_Should_TransitionCorrectly_BasedOnCurrentState(BookingStatus initialStatus)
    {
        // Arrange
        var booking = BookingTestDataBuilder.Create()
            .WithFutureDates()
            .BuildReserved();

        // Simulate different initial states
        switch (initialStatus)
        {
            case BookingStatus.Confirmed:
                booking.Confirm(UtcNow.AddHours(1));
                break;
            case BookingStatus.Rejected:
                booking.Reject(UtcNow.AddHours(1));
                break;
            case BookingStatus.Completed:
                booking.Confirm(UtcNow.AddHours(1));
                booking.Complete(UtcNow.AddDays(15));
                break;
            case BookingStatus.Cancelled:
                booking.Confirm(UtcNow.AddHours(1));
                booking.Cancel(UtcNow.AddDays(5));
                break;
        }

        // Assert current status
        booking.Status.Should().Be(initialStatus);
    }

    [Fact]
    public void Booking_Should_CalculateCorrectPricing_WithAmenities()
    {
        // Arrange
        var apartment = ApartmentTestDataBuilder.Create()
            .WithPrice(100.0m)
            .WithCleaningFee(50.0m)
            .WithLuxuryAmenities()
            .Build();

        var user = UserTestDataBuilder.CreateDefault();
        var period = DateRange.Create(StartDate, EndDate);
        var pricingService = new PricingService();

        // Act
        var booking = Booking.Reserve(apartment, user.Id, period, UtcNow, pricingService);

        // Assert
        var expectedPriceForPeriod = new Money(100.0m * period.LengthInDays, Currency.Usd);
        var expectedTotalPrice = new Money(expectedPriceForPeriod.Amount + 50.0m, Currency.Usd);

        booking.PriceForPeriod.Should().Be(expectedPriceForPeriod);
        booking.CleaningFee.Amount.Should().Be(50.0m);
        booking.TotalPrice.Should().Be(expectedTotalPrice);
    }

    [Fact]
    public void Booking_Should_HandleZeroCleaningFee()
    {
        // Arrange
        var apartment = ApartmentTestDataBuilder.Create()
            .WithPrice(100.0m)
            .WithNoCleaningFee()
            .Build();

        var user = UserTestDataBuilder.CreateDefault();
        var period = DateRange.Create(StartDate, EndDate);
        var pricingService = new PricingService();

        // Act
        var booking = Booking.Reserve(apartment, user.Id, period, UtcNow, pricingService);

        // Assert
        booking.CleaningFee.Should().Be(Money.Zero());
        booking.TotalPrice.Amount.Should().Be(100.0m * period.LengthInDays);
    }

    [Fact]
    public void Booking_Should_HandleDifferentCurrencies()
    {
        // Arrange
        var apartment = ApartmentTestDataBuilder.Create()
            .WithPrice(100.0m, Currency.Eur)
            .WithCleaningFee(50.0m, Currency.Eur)
            .Build();

        var user = UserTestDataBuilder.CreateDefault();
        var period = DateRange.Create(StartDate, EndDate);
        var pricingService = new PricingService();

        // Act
        var booking = Booking.Reserve(apartment, user.Id, period, UtcNow, pricingService);

        // Assert
        booking.PriceForPeriod.Currency.Should().Be(Currency.Eur);
        booking.CleaningFee.Currency.Should().Be(Currency.Eur);
        booking.TotalPrice.Currency.Should().Be(Currency.Eur);
    }

    [Fact]
    public void Booking_Should_ClearDomainEvents_AfterGettingThem()
    {
        // Arrange
        var booking = BookingTestDataBuilder.CreateDefaultReserved();

        // Act
        var events = booking.GetDomainEvents();
        booking.ClearDomainEvents();

        // Assert
        events.Should().NotBeEmpty();
        booking.GetDomainEvents().Should().BeEmpty();
    }

    [Fact]
    public void Booking_Should_HandleMultipleStatusTransitions()
    {
        // Arrange
        var booking = BookingTestDataBuilder.Create()
            .WithFutureDates()
            .BuildReserved();

        // Act & Assert - Reserve to Confirm
        var confirmResult = booking.Confirm(UtcNow.AddHours(1));
        confirmResult.IsSuccess.Should().BeTrue();
        booking.Status.Should().Be(BookingStatus.Confirmed);

        // Act & Assert - Confirm to Cancel
        var cancelResult = booking.Cancel(UtcNow.AddDays(5));
        cancelResult.IsSuccess.Should().BeTrue();
        booking.Status.Should().Be(BookingStatus.Cancelled);
    }

    [Fact]
    public void Booking_Should_PreventInvalidStatusTransitions()
    {
        // Arrange
        var booking = BookingTestDataBuilder.CreateDefaultCompleted();

        // Act & Assert - Try to confirm a completed booking
        var confirmResult = booking.Confirm(UtcNow.AddDays(20));
        confirmResult.IsFailure.Should().BeTrue();
        confirmResult.Error.Should().Be(BookingErrors.NotReserved);

        // Act & Assert - Try to reject a completed booking
        var rejectResult = booking.Reject(UtcNow.AddDays(20));
        rejectResult.IsFailure.Should().BeTrue();
        rejectResult.Error.Should().Be(BookingErrors.NotReserved);
    }
}
