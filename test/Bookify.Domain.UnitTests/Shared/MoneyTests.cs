using Bookify.Domain.Shared;
using FluentAssertions;

namespace Bookify.Domain.UnitTests.Shared;

public class MoneyTests : BaseTest
{
    [Fact]
    public void Constructor_Should_CreateMoney_WithValidAmountAndCurrency()
    {
        // Arrange
        var amount = 100.50m;
        var currency = Currency.Usd;

        // Act
        var money = new Money(amount, currency);

        // Assert
        money.Amount.Should().Be(amount);
        money.Currency.Should().Be(currency);
    }

    [Fact]
    public void Zero_Should_ReturnMoneyWithZeroAmount()
    {
        // Act
        var money = Money.Zero();

        // Assert
        money.Amount.Should().Be(0);
        money.Currency.Should().Be(Currency.None);
    }

    [Fact]
    public void Zero_Should_ReturnMoneyWithZeroAmountAndSpecificCurrency()
    {
        // Arrange
        var currency = Currency.Eur;

        // Act
        var money = Money.Zero(currency);

        // Assert
        money.Amount.Should().Be(0);
        money.Currency.Should().Be(currency);
    }

    [Fact]
    public void IsZero_Should_ReturnTrue_WhenAmountIsZero()
    {
        // Arrange
        var money = new Money(0, Currency.Usd);

        // Act & Assert
        money.IsZero().Should().BeTrue();
    }

    [Fact]
    public void IsZero_Should_ReturnFalse_WhenAmountIsNotZero()
    {
        // Arrange
        var money = new Money(100, Currency.Usd);

        // Act & Assert
        money.IsZero().Should().BeFalse();
    }

    [Theory]
    [InlineData(0)]
    [InlineData(100)]
    [InlineData(-50)]
    [InlineData(999999.99)]
    public void Constructor_Should_AcceptValidAmounts(decimal amount)
    {
        // Act
        var money = new Money(amount, Currency.Usd);

        // Assert
        money.Amount.Should().Be(amount);
    }

    [Fact]
    public void Equals_Should_ReturnTrue_WhenMoneyObjectsAreEqual()
    {
        // Arrange
        var money1 = new Money(100, Currency.Usd);
        var money2 = new Money(100, Currency.Usd);

        // Act & Assert
        money1.Should().Be(money2);
        money1.Equals(money2).Should().BeTrue();
        (money1 == money2).Should().BeTrue();
        (money1 != money2).Should().BeFalse();
    }

    [Fact]
    public void Equals_Should_ReturnFalse_WhenAmountsAreDifferent()
    {
        // Arrange
        var money1 = new Money(100, Currency.Usd);
        var money2 = new Money(200, Currency.Usd);

        // Act & Assert
        money1.Should().NotBe(money2);
        money1.Equals(money2).Should().BeFalse();
        (money1 == money2).Should().BeFalse();
        (money1 != money2).Should().BeTrue();
    }

    [Fact]
    public void Equals_Should_ReturnFalse_WhenCurrenciesAreDifferent()
    {
        // Arrange
        var money1 = new Money(100, Currency.Usd);
        var money2 = new Money(100, Currency.Eur);

        // Act & Assert
        money1.Should().NotBe(money2);
        money1.Equals(money2).Should().BeFalse();
        (money1 == money2).Should().BeFalse();
        (money1 != money2).Should().BeTrue();
    }

    [Fact]
    public void GetHashCode_Should_ReturnSameValue_ForEqualMoneyObjects()
    {
        // Arrange
        var money1 = new Money(100, Currency.Usd);
        var money2 = new Money(100, Currency.Usd);

        // Act & Assert
        money1.GetHashCode().Should().Be(money2.GetHashCode());
    }

    [Fact]
    public void GetHashCode_Should_ReturnDifferentValues_ForDifferentMoneyObjects()
    {
        // Arrange
        var money1 = new Money(100, Currency.Usd);
        var money2 = new Money(200, Currency.Usd);

        // Act & Assert
        money1.GetHashCode().Should().NotBe(money2.GetHashCode());
    }

    [Fact]
    public void Addition_Should_AddAmounts_WhenCurrenciesAreTheSame()
    {
        // Arrange
        var money1 = new Money(100, Currency.Usd);
        var money2 = new Money(50, Currency.Usd);

        // Act
        var result = money1 + money2;

        // Assert
        result.Amount.Should().Be(150);
        result.Currency.Should().Be(Currency.Usd);
    }

    [Fact]
    public void Addition_Should_ThrowInvalidOperationException_WhenCurrenciesAreDifferent()
    {
        // Arrange
        var money1 = new Money(100, Currency.Usd);
        var money2 = new Money(50, Currency.Eur);

        // Act & Assert
        var act = () => money1 + money2;
        act.Should().Throw<InvalidOperationException>()
            .WithMessage("Currencies have to be equal");
    }

    [Fact]
    public void Subtraction_Should_SubtractAmounts_WhenCurrenciesAreTheSame()
    {
        // Arrange
        var money1 = new Money(100, Currency.Usd);
        var money2 = new Money(30, Currency.Usd);

        // Act
        var result = money1 - money2;

        // Assert
        result.Amount.Should().Be(70);
        result.Currency.Should().Be(Currency.Usd);
    }

    [Fact]
    public void Subtraction_Should_ThrowInvalidOperationException_WhenCurrenciesAreDifferent()
    {
        // Arrange
        var money1 = new Money(100, Currency.Usd);
        var money2 = new Money(30, Currency.Eur);

        // Act & Assert
        var act = () => money1 - money2;
        act.Should().Throw<InvalidOperationException>()
            .WithMessage("Currencies have to be equal");
    }

    [Theory]
    [InlineData(100, 2, 200)]
    [InlineData(50, 0, 0)]
    [InlineData(25, 4, 100)]
    [InlineData(100, -1, -100)]
    public void Multiplication_Should_MultiplyAmountByScalar(decimal amount, decimal multiplier, decimal expectedResult)
    {
        // Arrange
        var money = new Money(amount, Currency.Usd);

        // Act
        var result = money * multiplier;

        // Assert
        result.Amount.Should().Be(expectedResult);
        result.Currency.Should().Be(Currency.Usd);
    }

    [Fact]
    public void ToString_Should_ReturnFormattedString()
    {
        // Arrange
        var money = new Money(100.50m, Currency.Usd);

        // Act
        var result = money.ToString();

        // Assert
        result.Should().Contain("100.50");
        result.Should().Contain("USD");
    }

    [Fact]
    public void Money_Should_HandleDecimalPrecision()
    {
        // Arrange
        var money1 = new Money(10.123456789m, Currency.Usd);
        var money2 = new Money(5.987654321m, Currency.Usd);

        // Act
        var sum = money1 + money2;

        // Assert
        sum.Amount.Should().Be(10.123456789m + 5.987654321m);
    }

    [Fact]
    public void Money_Should_SupportNegativeAmounts()
    {
        // Arrange
        var positiveAmount = new Money(100, Currency.Usd);
        var negativeAmount = new Money(-50, Currency.Usd);

        // Act
        var result = positiveAmount + negativeAmount;

        // Assert
        result.Amount.Should().Be(50);
        result.Currency.Should().Be(Currency.Usd);
    }

    [Theory]
    [InlineData(Currency.Usd)]
    [InlineData(Currency.Eur)]
    [InlineData(Currency.None)]
    public void Money_Should_SupportDifferentCurrencies(Currency currency)
    {
        // Arrange & Act
        var money = new Money(100, currency);

        // Assert
        money.Currency.Should().Be(currency);
    }

    [Fact]
    public void Money_Should_BeImmutable()
    {
        // Arrange
        var originalMoney = new Money(100, Currency.Usd);
        var originalAmount = originalMoney.Amount;
        var originalCurrency = originalMoney.Currency;

        // Act - Perform operations that create new instances
        var addedMoney = originalMoney + new Money(50, Currency.Usd);
        var multipliedMoney = originalMoney * 2;

        // Assert - Original money should remain unchanged
        originalMoney.Amount.Should().Be(originalAmount);
        originalMoney.Currency.Should().Be(originalCurrency);
        
        // New instances should have different values
        addedMoney.Amount.Should().Be(150);
        multipliedMoney.Amount.Should().Be(200);
    }
}
