using Bookify.Domain.Entities.Bookings.ValueObjects;
using FluentAssertions;

namespace Bookify.Domain.UnitTests.Shared;

public class DateRangeTests : BaseTest
{
    [Fact]
    public void Create_Should_ReturnDateRange_WhenDatesAreValid()
    {
        // Arrange
        var startDate = new DateOnly(2024, 1, 1);
        var endDate = new DateOnly(2024, 1, 10);

        // Act
        var dateRange = DateRange.Create(startDate, endDate);

        // Assert
        dateRange.Start.Should().Be(startDate);
        dateRange.End.Should().Be(endDate);
    }

    [Fact]
    public void Create_Should_ThrowArgumentException_WhenStartDateIsAfterEndDate()
    {
        // Arrange
        var startDate = new DateOnly(2024, 1, 10);
        var endDate = new DateOnly(2024, 1, 1);

        // Act & Assert
        var act = () => DateRange.Create(startDate, endDate);
        act.Should().Throw<ArgumentException>()
            .WithMessage("End date precedes start date");
    }

    [Fact]
    public void Create_Should_AllowSameDateForStartAndEnd()
    {
        // Arrange
        var date = new DateOnly(2024, 1, 1);

        // Act
        var dateRange = DateRange.Create(date, date);

        // Assert
        dateRange.Start.Should().Be(date);
        dateRange.End.Should().Be(date);
        dateRange.LengthInDays.Should().Be(1);
    }

    [Theory]
    [InlineData("2024-01-01", "2024-01-01", 1)]
    [InlineData("2024-01-01", "2024-01-02", 2)]
    [InlineData("2024-01-01", "2024-01-10", 10)]
    [InlineData("2024-01-01", "2024-01-31", 31)]
    [InlineData("2024-02-01", "2024-02-29", 29)] // Leap year
    [InlineData("2024-01-01", "2024-12-31", 366)] // Full leap year
    public void LengthInDays_Should_CalculateCorrectly(string startDateStr, string endDateStr, int expectedDays)
    {
        // Arrange
        var startDate = DateOnly.Parse(startDateStr);
        var endDate = DateOnly.Parse(endDateStr);
        var dateRange = DateRange.Create(startDate, endDate);

        // Act & Assert
        dateRange.LengthInDays.Should().Be(expectedDays);
    }

    [Fact]
    public void Equals_Should_ReturnTrue_WhenDateRangesAreEqual()
    {
        // Arrange
        var startDate = new DateOnly(2024, 1, 1);
        var endDate = new DateOnly(2024, 1, 10);
        var dateRange1 = DateRange.Create(startDate, endDate);
        var dateRange2 = DateRange.Create(startDate, endDate);

        // Act & Assert
        dateRange1.Should().Be(dateRange2);
        dateRange1.Equals(dateRange2).Should().BeTrue();
        (dateRange1 == dateRange2).Should().BeTrue();
        (dateRange1 != dateRange2).Should().BeFalse();
    }

    [Fact]
    public void Equals_Should_ReturnFalse_WhenDateRangesAreDifferent()
    {
        // Arrange
        var dateRange1 = DateRange.Create(new DateOnly(2024, 1, 1), new DateOnly(2024, 1, 10));
        var dateRange2 = DateRange.Create(new DateOnly(2024, 1, 2), new DateOnly(2024, 1, 10));

        // Act & Assert
        dateRange1.Should().NotBe(dateRange2);
        dateRange1.Equals(dateRange2).Should().BeFalse();
        (dateRange1 == dateRange2).Should().BeFalse();
        (dateRange1 != dateRange2).Should().BeTrue();
    }

    [Fact]
    public void GetHashCode_Should_ReturnSameValue_ForEqualDateRanges()
    {
        // Arrange
        var startDate = new DateOnly(2024, 1, 1);
        var endDate = new DateOnly(2024, 1, 10);
        var dateRange1 = DateRange.Create(startDate, endDate);
        var dateRange2 = DateRange.Create(startDate, endDate);

        // Act & Assert
        dateRange1.GetHashCode().Should().Be(dateRange2.GetHashCode());
    }

    [Fact]
    public void GetHashCode_Should_ReturnDifferentValues_ForDifferentDateRanges()
    {
        // Arrange
        var dateRange1 = DateRange.Create(new DateOnly(2024, 1, 1), new DateOnly(2024, 1, 10));
        var dateRange2 = DateRange.Create(new DateOnly(2024, 1, 2), new DateOnly(2024, 1, 10));

        // Act & Assert
        dateRange1.GetHashCode().Should().NotBe(dateRange2.GetHashCode());
    }

    [Fact]
    public void ToString_Should_ReturnFormattedString()
    {
        // Arrange
        var startDate = new DateOnly(2024, 1, 1);
        var endDate = new DateOnly(2024, 1, 10);
        var dateRange = DateRange.Create(startDate, endDate);

        // Act
        var result = dateRange.ToString();

        // Assert
        result.Should().Contain("2024-01-01");
        result.Should().Contain("2024-01-10");
    }

    [Theory]
    [InlineData("2024-01-01", "2024-01-05", "2024-01-03", "2024-01-07", true)]  // Overlapping
    [InlineData("2024-01-01", "2024-01-05", "2024-01-05", "2024-01-10", true)] // Adjacent (same end/start)
    [InlineData("2024-01-01", "2024-01-05", "2024-01-06", "2024-01-10", false)] // Non-overlapping
    [InlineData("2024-01-03", "2024-01-07", "2024-01-01", "2024-01-05", true)]  // Overlapping (reverse)
    [InlineData("2024-01-01", "2024-01-10", "2024-01-03", "2024-01-07", true)]  // Contained within
    [InlineData("2024-01-03", "2024-01-07", "2024-01-01", "2024-01-10", true)]  // Contains
    public void OverlapsWith_Should_DetectOverlaps_Correctly(
        string start1Str, string end1Str, 
        string start2Str, string end2Str, 
        bool expectedOverlap)
    {
        // Arrange
        var dateRange1 = DateRange.Create(DateOnly.Parse(start1Str), DateOnly.Parse(end1Str));
        var dateRange2 = DateRange.Create(DateOnly.Parse(start2Str), DateOnly.Parse(end2Str));

        // Act
        var overlaps = dateRange1.OverlapsWith(dateRange2);

        // Assert
        overlaps.Should().Be(expectedOverlap);
        // Test symmetry
        dateRange2.OverlapsWith(dateRange1).Should().Be(expectedOverlap);
    }

    [Fact]
    public void Contains_Should_ReturnTrue_WhenDateIsWithinRange()
    {
        // Arrange
        var dateRange = DateRange.Create(new DateOnly(2024, 1, 1), new DateOnly(2024, 1, 10));
        var dateToCheck = new DateOnly(2024, 1, 5);

        // Act
        var contains = dateRange.Contains(dateToCheck);

        // Assert
        contains.Should().BeTrue();
    }

    [Fact]
    public void Contains_Should_ReturnTrue_WhenDateIsStartOrEndDate()
    {
        // Arrange
        var startDate = new DateOnly(2024, 1, 1);
        var endDate = new DateOnly(2024, 1, 10);
        var dateRange = DateRange.Create(startDate, endDate);

        // Act & Assert
        dateRange.Contains(startDate).Should().BeTrue();
        dateRange.Contains(endDate).Should().BeTrue();
    }

    [Fact]
    public void Contains_Should_ReturnFalse_WhenDateIsOutsideRange()
    {
        // Arrange
        var dateRange = DateRange.Create(new DateOnly(2024, 1, 1), new DateOnly(2024, 1, 10));
        var dateBefore = new DateOnly(2023, 12, 31);
        var dateAfter = new DateOnly(2024, 1, 11);

        // Act & Assert
        dateRange.Contains(dateBefore).Should().BeFalse();
        dateRange.Contains(dateAfter).Should().BeFalse();
    }

    [Fact]
    public void IsValid_Should_ReturnTrue_WhenStartDateIsBeforeOrEqualToEndDate()
    {
        // Arrange & Act & Assert
        DateRange.IsValid(new DateOnly(2024, 1, 1), new DateOnly(2024, 1, 10)).Should().BeTrue();
        DateRange.IsValid(new DateOnly(2024, 1, 1), new DateOnly(2024, 1, 1)).Should().BeTrue();
    }

    [Fact]
    public void IsValid_Should_ReturnFalse_WhenStartDateIsAfterEndDate()
    {
        // Arrange & Act & Assert
        DateRange.IsValid(new DateOnly(2024, 1, 10), new DateOnly(2024, 1, 1)).Should().BeFalse();
    }
}
