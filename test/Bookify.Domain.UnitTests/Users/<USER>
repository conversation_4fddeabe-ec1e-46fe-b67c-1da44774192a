﻿using Bookify.Domain.Entities.Users;
using Bookify.Domain.Entities.Users.Events;
using Bookify.Domain.Entities.Users.ValueObjects;
using Bookify.Domain.UnitTests.TestBuilders;
using FluentAssertions;

namespace Bookify.Domain.UnitTests.Users;
public class UserTests : BaseTest
{
    [Fact]
    public void Create_Should_Raise_UserCreatedDomainEvent()
    {
        //Act
        var user = User.Create(UserData.FirstName, UserData.LastName, UserData.Email);

        //Assert
        var userCreatedDomainEvent = AssertDomainEventWasPublished<UserCreatedDomainEvent>(user);

        userCreatedDomainEvent.UserId.Should().Be(user.Id);
    }

    [Fact]
    public void Create_Should_AddRegisteredRoleToUser()
    {
        //Act
        var user = User.Create(UserData.FirstName, UserData.LastName, UserData.Email);

        //Assert
        user.Roles.Should().Contain(Role.Registered);
    }

    [Fact]
    public void Create_Should_SetCorrectUserProperties()
    {
        // Arrange
        var firstName = "John";
        var lastName = "Doe";
        var email = new Email("<EMAIL>");

        // Act
        var user = User.Create(firstName, lastName, email);

        // Assert
        user.FirstName.Should().Be(firstName);
        user.LastName.Should().Be(lastName);
        user.Email.Should().Be(email);
        user.Id.Should().NotBe(default);
        user.IdentityId.Should().BeEmpty(); // Initially empty until set by identity provider
    }

    [Fact]
    public void Create_Should_GenerateUniqueIds_ForDifferentUsers()
    {
        // Act
        var user1 = User.Create("John", "Doe", new Email("<EMAIL>"));
        var user2 = User.Create("Jane", "Smith", new Email("<EMAIL>"));

        // Assert
        user1.Id.Should().NotBe(user2.Id);
    }

    [Fact]
    public void Create_Should_OnlyAddRegisteredRole_ByDefault()
    {
        // Act
        var user = User.Create(UserData.FirstName, UserData.LastName, UserData.Email);

        // Assert
        user.Roles.Should().HaveCount(1);
        user.Roles.Should().Contain(Role.Registered);
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void Create_Should_HandleInvalidFirstName(string invalidFirstName)
    {
        // Act & Assert
        var act = () => User.Create(invalidFirstName, "LastName", UserData.Email);

        // Note: This test assumes validation exists. If not, this test documents expected behavior
        // and can be updated when validation is added
        if (string.IsNullOrWhiteSpace(invalidFirstName))
        {
            // For now, we'll just verify the user is created but with the invalid name
            // In a real scenario, this might throw an exception
            var user = act();
            user.FirstName.Should().Be(invalidFirstName);
        }
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public void Create_Should_HandleInvalidLastName(string invalidLastName)
    {
        // Act & Assert
        var act = () => User.Create("FirstName", invalidLastName, UserData.Email);

        // Note: This test assumes validation exists. If not, this test documents expected behavior
        if (string.IsNullOrWhiteSpace(invalidLastName))
        {
            var user = act();
            user.LastName.Should().Be(invalidLastName);
        }
    }

    [Fact]
    public void User_Should_BeEntity_WithCorrectId()
    {
        // Act
        var user = User.Create(UserData.FirstName, UserData.LastName, UserData.Email);

        // Assert
        user.Should().BeAssignableTo<Entity<UserId>>();
        user.Id.Should().BeOfType<UserId>();
        user.Id.Value.Should().NotBe(Guid.Empty);
    }

    [Fact]
    public void User_Should_ClearDomainEvents_AfterGettingThem()
    {
        // Arrange
        var user = User.Create(UserData.FirstName, UserData.LastName, UserData.Email);

        // Act
        var events = user.GetDomainEvents();
        user.ClearDomainEvents();

        // Assert
        events.Should().NotBeEmpty();
        user.GetDomainEvents().Should().BeEmpty();
    }

    [Fact]
    public void User_Should_SupportMultipleRoles()
    {
        // Arrange
        var user = User.Create(UserData.FirstName, UserData.LastName, UserData.Email);

        // Act - In a real scenario, there might be methods to add roles
        // For now, we verify the initial state and collection type
        var roles = user.Roles;

        // Assert
        roles.Should().BeAssignableTo<IReadOnlyCollection<Role>>();
        roles.Should().Contain(Role.Registered);
    }

    [Fact]
    public void User_Should_HaveReadOnlyRolesCollection()
    {
        // Arrange
        var user = User.Create(UserData.FirstName, UserData.LastName, UserData.Email);

        // Act & Assert
        user.Roles.Should().BeAssignableTo<IReadOnlyCollection<Role>>();
        // Verify we can't cast to a mutable collection
        user.Roles.Should().NotBeAssignableTo<ICollection<Role>>();
    }

    [Fact]
    public void UserTestDataBuilder_Should_CreateValidUsers()
    {
        // Act
        var user1 = UserTestDataBuilder.CreateDefault();
        var user2 = UserTestDataBuilder.CreateWithEmail("<EMAIL>");
        var user3 = UserTestDataBuilder.CreateWithName("Custom", "Name");

        // Assert
        user1.Should().NotBeNull();
        user1.Roles.Should().Contain(Role.Registered);

        user2.Email.Value.Should().Be("<EMAIL>");

        user3.FirstName.Should().Be("Custom");
        user3.LastName.Should().Be("Name");
    }

    [Fact]
    public void UserTestDataBuilder_Should_CreateMultipleUniqueUsers()
    {
        // Act
        var users = UserTestDataBuilder.CreateMultiple(3);

        // Assert
        users.Should().HaveCount(3);
        users.Select(u => u.Id).Should().OnlyHaveUniqueItems();
        users.Select(u => u.Email.Value).Should().OnlyHaveUniqueItems();
    }
}
