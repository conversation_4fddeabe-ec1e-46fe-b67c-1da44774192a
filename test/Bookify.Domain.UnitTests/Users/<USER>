using Bookify.Domain.Entities.Users.ValueObjects;
using FluentAssertions;

namespace Bookify.Domain.UnitTests.Users;

public class EmailTests : BaseTest
{
    [Theory]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    public void Constructor_Should_CreateEmail_WithValidEmailAddress(string validEmail)
    {
        // Act
        var email = new Email(validEmail);

        // Assert
        email.Value.Should().Be(validEmail);
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData("invalid-email")]
    [InlineData("@domain.com")]
    [InlineData("user@")]
    [InlineData("user@.com")]
    [InlineData("<EMAIL>")]
    [InlineData("user@domain")]
    [InlineData(null)]
    public void Constructor_Should_ThrowArgumentException_WithInvalidEmailAddress(string invalidEmail)
    {
        // Act & Assert
        var act = () => new Email(invalidEmail);
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void Equals_Should_ReturnTrue_WhenEmailsAreEqual()
    {
        // Arrange
        var email1 = new Email("<EMAIL>");
        var email2 = new Email("<EMAIL>");

        // Act & Assert
        email1.Should().Be(email2);
        email1.Equals(email2).Should().BeTrue();
        (email1 == email2).Should().BeTrue();
        (email1 != email2).Should().BeFalse();
    }

    [Fact]
    public void Equals_Should_ReturnFalse_WhenEmailsAreDifferent()
    {
        // Arrange
        var email1 = new Email("<EMAIL>");
        var email2 = new Email("<EMAIL>");

        // Act & Assert
        email1.Should().NotBe(email2);
        email1.Equals(email2).Should().BeFalse();
        (email1 == email2).Should().BeFalse();
        (email1 != email2).Should().BeTrue();
    }

    [Fact]
    public void Equals_Should_BeCaseInsensitive()
    {
        // Arrange
        var email1 = new Email("<EMAIL>");
        var email2 = new Email("<EMAIL>");

        // Act & Assert
        email1.Should().Be(email2);
        email1.Equals(email2).Should().BeTrue();
    }

    [Fact]
    public void GetHashCode_Should_ReturnSameValue_ForEqualEmails()
    {
        // Arrange
        var email1 = new Email("<EMAIL>");
        var email2 = new Email("<EMAIL>");

        // Act & Assert
        email1.GetHashCode().Should().Be(email2.GetHashCode());
    }

    [Fact]
    public void GetHashCode_Should_ReturnSameValue_ForCaseInsensitiveEmails()
    {
        // Arrange
        var email1 = new Email("<EMAIL>");
        var email2 = new Email("<EMAIL>");

        // Act & Assert
        email1.GetHashCode().Should().Be(email2.GetHashCode());
    }

    [Fact]
    public void GetHashCode_Should_ReturnDifferentValues_ForDifferentEmails()
    {
        // Arrange
        var email1 = new Email("<EMAIL>");
        var email2 = new Email("<EMAIL>");

        // Act & Assert
        email1.GetHashCode().Should().NotBe(email2.GetHashCode());
    }

    [Fact]
    public void ToString_Should_ReturnEmailValue()
    {
        // Arrange
        var emailValue = "<EMAIL>";
        var email = new Email(emailValue);

        // Act
        var result = email.ToString();

        // Assert
        result.Should().Be(emailValue);
    }

    [Fact]
    public void ImplicitConversion_Should_ConvertEmailToString()
    {
        // Arrange
        var email = new Email("<EMAIL>");

        // Act
        string emailString = email;

        // Assert
        emailString.Should().Be("<EMAIL>");
    }

    [Fact]
    public void Email_Should_NormalizeToLowerCase()
    {
        // Arrange
        var upperCaseEmail = "<EMAIL>";

        // Act
        var email = new Email(upperCaseEmail);

        // Assert
        email.Value.Should().Be("<EMAIL>");
    }

    [Fact]
    public void Email_Should_TrimWhitespace()
    {
        // Arrange
        var emailWithWhitespace = "  <EMAIL>  ";

        // Act
        var email = new Email(emailWithWhitespace);

        // Assert
        email.Value.Should().Be("<EMAIL>");
    }

    [Theory]
    [InlineData("<EMAIL>", "domain.com")]
    [InlineData("<EMAIL>", "example.co.uk")]
    [InlineData("<EMAIL>", "sub.domain.org")]
    public void GetDomain_Should_ReturnCorrectDomain(string emailAddress, string expectedDomain)
    {
        // Arrange
        var email = new Email(emailAddress);

        // Act
        var domain = email.GetDomain();

        // Assert
        domain.Should().Be(expectedDomain);
    }

    [Theory]
    [InlineData("<EMAIL>", "user")]
    [InlineData("<EMAIL>", "test.user")]
    [InlineData("<EMAIL>", "user+tag")]
    public void GetLocalPart_Should_ReturnCorrectLocalPart(string emailAddress, string expectedLocalPart)
    {
        // Arrange
        var email = new Email(emailAddress);

        // Act
        var localPart = email.GetLocalPart();

        // Assert
        localPart.Should().Be(expectedLocalPart);
    }

    [Fact]
    public void Email_Should_BeValueObject()
    {
        // Arrange
        var email1 = new Email("<EMAIL>");
        var email2 = new Email("<EMAIL>");
        var email3 = new Email("<EMAIL>");

        // Assert - Value objects should be equal based on value, not reference
        email1.Should().Be(email2);
        email1.Should().NotBe(email3);
        
        // Should not be the same reference
        ReferenceEquals(email1, email2).Should().BeFalse();
    }

    [Fact]
    public void Email_Should_BeImmutable()
    {
        // Arrange
        var originalEmailValue = "<EMAIL>";
        var email = new Email(originalEmailValue);

        // Act - Try to access the value (no mutation methods should exist)
        var value = email.Value;

        // Assert
        value.Should().Be(originalEmailValue);
        // The Email class should not have any methods that modify its state
        // This is enforced by the design of the value object
    }

    [Theory]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    public void Email_Should_HandleVariousLengths(string emailAddress)
    {
        // Act
        var email = new Email(emailAddress);

        // Assert
        email.Value.Should().Be(emailAddress.ToLowerInvariant());
    }

    [Theory]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    public void Email_Should_HandleCommonDomains(string emailAddress)
    {
        // Act
        var email = new Email(emailAddress);

        // Assert
        email.Value.Should().Be(emailAddress.ToLowerInvariant());
    }
}
