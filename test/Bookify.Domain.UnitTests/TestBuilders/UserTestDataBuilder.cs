using Bookify.Domain.Entities.Users;
using Bookify.Domain.Entities.Users.ValueObjects;

namespace Bookify.Domain.UnitTests.TestBuilders;

/// <summary>
/// Test data builder for creating User entities with fluent API
/// </summary>
public class UserTestDataBuilder
{
    private string _firstName;
    private string _lastName;
    private Email _email;
    private string _identityId;

    public UserTestDataBuilder()
    {
        // Set default values
        _firstName = "John";
        _lastName = "Doe";
        _email = new Email("<EMAIL>");
        _identityId = Guid.NewGuid().ToString();
    }

    public UserTestDataBuilder WithFirstName(string firstName)
    {
        _firstName = firstName;
        return this;
    }

    public UserTestDataBuilder WithLastName(string lastName)
    {
        _lastName = lastName;
        return this;
    }

    public UserTestDataBuilder WithEmail(Email email)
    {
        _email = email;
        return this;
    }

    public UserTestDataBuilder WithEmail(string email)
    {
        _email = new Email(email);
        return this;
    }

    public UserTestDataBuilder WithIdentityId(string identityId)
    {
        _identityId = identityId;
        return this;
    }

    public UserTestDataBuilder WithFullName(string firstName, string lastName)
    {
        _firstName = firstName;
        _lastName = lastName;
        return this;
    }

    public User Build()
    {
        var user = User.Create(_firstName, _lastName, _email);
        
        // Set IdentityId using reflection for testing purposes
        if (!string.IsNullOrEmpty(_identityId))
        {
            var property = typeof(User).GetProperty(nameof(User.IdentityId));
            property?.SetValue(user, _identityId);
        }

        return user;
    }

    /// <summary>
    /// Static factory method for fluent API
    /// </summary>
    public static UserTestDataBuilder Create() => new();

    /// <summary>
    /// Creates a user with default values
    /// </summary>
    public static User CreateDefault() => new UserTestDataBuilder().Build();

    /// <summary>
    /// Creates a user with specific email
    /// </summary>
    public static User CreateWithEmail(string email) => new UserTestDataBuilder()
        .WithEmail(email)
        .Build();

    /// <summary>
    /// Creates a user with specific name
    /// </summary>
    public static User CreateWithName(string firstName, string lastName) => new UserTestDataBuilder()
        .WithFullName(firstName, lastName)
        .Build();

    /// <summary>
    /// Creates multiple users for testing scenarios
    /// </summary>
    public static List<User> CreateMultiple(int count)
    {
        var users = new List<User>();
        for (int i = 1; i <= count; i++)
        {
            users.Add(new UserTestDataBuilder()
                .WithFirstName($"User{i}")
                .WithLastName($"Test{i}")
                .WithEmail($"user{i}@test.com")
                .Build());
        }
        return users;
    }

    /// <summary>
    /// Creates a user with a specific identity ID (useful for authentication tests)
    /// </summary>
    public static User CreateWithIdentityId(string identityId) => new UserTestDataBuilder()
        .WithIdentityId(identityId)
        .Build();

    /// <summary>
    /// Creates users with different roles for testing authorization
    /// </summary>
    public static class Roles
    {
        public static User CreateRegisteredUser() => new UserTestDataBuilder()
            .WithFirstName("Registered")
            .WithLastName("User")
            .WithEmail("<EMAIL>")
            .Build();

        // Note: Additional roles would be added here as the domain evolves
        // For example: CreateAdminUser(), CreateModeratorUser(), etc.
    }

    /// <summary>
    /// Creates users for specific test scenarios
    /// </summary>
    public static class Scenarios
    {
        public static User CreateBookingOwner() => new UserTestDataBuilder()
            .WithFirstName("Booking")
            .WithLastName("Owner")
            .WithEmail("<EMAIL>")
            .Build();

        public static User CreateApartmentHost() => new UserTestDataBuilder()
            .WithFirstName("Apartment")
            .WithLastName("Host")
            .WithEmail("<EMAIL>")
            .Build();

        public static User CreateGuest() => new UserTestDataBuilder()
            .WithFirstName("Guest")
            .WithLastName("User")
            .WithEmail("<EMAIL>")
            .Build();

        public static (User user1, User user2) CreateTwoUsers() => 
            (CreateWithEmail("<EMAIL>"), CreateWithEmail("<EMAIL>"));

        public static (User owner, User otherUser) CreateOwnerAndOtherUser() =>
            (CreateBookingOwner(), CreateGuest());
    }
}
