using Bookify.Domain.Entities.Apartments;
using Bookify.Domain.Entities.Bookings;
using Bookify.Domain.Entities.Bookings.ValueObjects;
using Bookify.Domain.Entities.Users;
using Bookify.Domain.Shared;
using Bookify.Domain.UnitTests.Apartments;
using Bookify.Domain.UnitTests.Users;

namespace Bookify.Domain.UnitTests.TestBuilders;

/// <summary>
/// Test data builder for creating Booking entities with fluent API
/// </summary>
public class BookingTestDataBuilder
{
    private Apartment _apartment;
    private UserId _userId;
    private DateRange _duration;
    private DateTime _createdOnUtc;
    private PricingService _pricingService;

    public BookingTestDataBuilder()
    {
        // Set default values
        _apartment = ApartmentData.Create(new Money(100.0m, Currency.Usd), new Money(50.0m, Currency.Usd));
        _userId = User.Create(UserData.FirstName, UserData.LastName, UserData.Email).Id;
        _duration = DateRange.Create(new DateOnly(2024, 2, 1), new DateOnly(2024, 2, 10));
        _createdOnUtc = new DateTime(2024, 1, 1, 12, 0, 0, DateTimeKind.Utc);
        _pricingService = new PricingService();
    }

    public BookingTestDataBuilder WithApartment(Apartment apartment)
    {
        _apartment = apartment;
        return this;
    }

    public BookingTestDataBuilder WithApartment(Money price, Money? cleaningFee = null)
    {
        _apartment = ApartmentData.Create(price, cleaningFee);
        return this;
    }

    public BookingTestDataBuilder WithUserId(UserId userId)
    {
        _userId = userId;
        return this;
    }

    public BookingTestDataBuilder WithUser(User user)
    {
        _userId = user.Id;
        return this;
    }

    public BookingTestDataBuilder WithDuration(DateRange duration)
    {
        _duration = duration;
        return this;
    }

    public BookingTestDataBuilder WithDuration(DateOnly startDate, DateOnly endDate)
    {
        _duration = DateRange.Create(startDate, endDate);
        return this;
    }

    public BookingTestDataBuilder WithCreatedOnUtc(DateTime createdOnUtc)
    {
        _createdOnUtc = createdOnUtc;
        return this;
    }

    public BookingTestDataBuilder WithPricingService(PricingService pricingService)
    {
        _pricingService = pricingService;
        return this;
    }

    /// <summary>
    /// Creates a booking in Reserved status
    /// </summary>
    public Booking BuildReserved()
    {
        return Booking.Reserve(_apartment, _userId, _duration, _createdOnUtc, _pricingService);
    }

    /// <summary>
    /// Creates a booking in Confirmed status
    /// </summary>
    public Booking BuildConfirmed(DateTime? confirmedOnUtc = null)
    {
        var booking = BuildReserved();
        booking.Confirm(confirmedOnUtc ?? _createdOnUtc.AddHours(1));
        return booking;
    }

    /// <summary>
    /// Creates a booking in Rejected status
    /// </summary>
    public Booking BuildRejected(DateTime? rejectedOnUtc = null)
    {
        var booking = BuildReserved();
        booking.Reject(rejectedOnUtc ?? _createdOnUtc.AddHours(1));
        return booking;
    }

    /// <summary>
    /// Creates a booking in Completed status
    /// </summary>
    public Booking BuildCompleted(DateTime? confirmedOnUtc = null, DateTime? completedOnUtc = null)
    {
        var booking = BuildConfirmed(confirmedOnUtc);
        booking.Complete(completedOnUtc ?? _createdOnUtc.AddDays(15));
        return booking;
    }

    /// <summary>
    /// Creates a booking in Cancelled status
    /// </summary>
    public Booking BuildCancelled(DateTime? confirmedOnUtc = null, DateTime? cancelledOnUtc = null)
    {
        var booking = BuildConfirmed(confirmedOnUtc);
        booking.Cancel(cancelledOnUtc ?? _createdOnUtc.AddDays(5));
        return booking;
    }

    /// <summary>
    /// Creates a booking for a future date range (useful for cancellation tests)
    /// </summary>
    public BookingTestDataBuilder WithFutureDates(int daysFromNow = 30)
    {
        var startDate = DateOnly.FromDateTime(_createdOnUtc.AddDays(daysFromNow));
        var endDate = startDate.AddDays(9);
        return WithDuration(startDate, endDate);
    }

    /// <summary>
    /// Creates a booking for a past date range (useful for completion tests)
    /// </summary>
    public BookingTestDataBuilder WithPastDates(int daysAgo = 30)
    {
        var startDate = DateOnly.FromDateTime(_createdOnUtc.AddDays(-daysAgo));
        var endDate = startDate.AddDays(9);
        return WithDuration(startDate, endDate);
    }

    /// <summary>
    /// Creates a booking that has already started (useful for cancellation failure tests)
    /// </summary>
    public BookingTestDataBuilder WithStartedDates()
    {
        var startDate = DateOnly.FromDateTime(_createdOnUtc.AddDays(-5));
        var endDate = DateOnly.FromDateTime(_createdOnUtc.AddDays(5));
        return WithDuration(startDate, endDate);
    }

    /// <summary>
    /// Static factory method for fluent API
    /// </summary>
    public static BookingTestDataBuilder Create() => new();

    /// <summary>
    /// Static factory method with default reserved booking
    /// </summary>
    public static Booking CreateDefaultReserved() => new BookingTestDataBuilder().BuildReserved();

    /// <summary>
    /// Static factory method with default confirmed booking
    /// </summary>
    public static Booking CreateDefaultConfirmed() => new BookingTestDataBuilder().BuildConfirmed();

    /// <summary>
    /// Static factory method with default completed booking
    /// </summary>
    public static Booking CreateDefaultCompleted() => new BookingTestDataBuilder().BuildCompleted();
}
