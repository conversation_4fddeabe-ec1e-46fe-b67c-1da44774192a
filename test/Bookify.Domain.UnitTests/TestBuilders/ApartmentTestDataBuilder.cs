using Bookify.Domain.Entities.Apartments;
using Bookify.Domain.Entities.Apartments.Enums;
using Bookify.Domain.Entities.Apartments.ValueObjects;
using Bookify.Domain.Shared;

namespace Bookify.Domain.UnitTests.TestBuilders;

/// <summary>
/// Test data builder for creating Apartment entities with fluent API
/// </summary>
public class ApartmentTestDataBuilder
{
    private ApartmentId _id;
    private string _name;
    private string _description;
    private Address _address;
    private Money _price;
    private Money _cleaningFee;
    private List<Amenity> _amenities;
    private DateTime? _lastBookedOnUtc;

    public ApartmentTestDataBuilder()
    {
        // Set default values
        _id = ApartmentId.New();
        _name = "Test Apartment";
        _description = "A beautiful test apartment with amazing views";
        _address = new Address("United States", "California", "90210", "Beverly Hills", "123 Test Street");
        _price = new Money(100.0m, Currency.Usd);
        _cleaningFee = new Money(50.0m, Currency.Usd);
        _amenities = new List<Amenity>();
        _lastBookedOnUtc = null;
    }

    public ApartmentTestDataBuilder WithId(ApartmentId id)
    {
        _id = id;
        return this;
    }

    public ApartmentTestDataBuilder WithName(string name)
    {
        _name = name;
        return this;
    }

    public ApartmentTestDataBuilder WithDescription(string description)
    {
        _description = description;
        return this;
    }

    public ApartmentTestDataBuilder WithAddress(Address address)
    {
        _address = address;
        return this;
    }

    public ApartmentTestDataBuilder WithAddress(string country, string state, string zipCode, string city, string street)
    {
        _address = new Address(country, state, zipCode, city, street);
        return this;
    }

    public ApartmentTestDataBuilder WithPrice(Money price)
    {
        _price = price;
        return this;
    }

    public ApartmentTestDataBuilder WithPrice(decimal amount, Currency currency = Currency.Usd)
    {
        _price = new Money(amount, currency);
        return this;
    }

    public ApartmentTestDataBuilder WithCleaningFee(Money cleaningFee)
    {
        _cleaningFee = cleaningFee;
        return this;
    }

    public ApartmentTestDataBuilder WithCleaningFee(decimal amount, Currency currency = Currency.Usd)
    {
        _cleaningFee = new Money(amount, currency);
        return this;
    }

    public ApartmentTestDataBuilder WithNoCleaningFee()
    {
        _cleaningFee = Money.Zero();
        return this;
    }

    public ApartmentTestDataBuilder WithAmenities(params Amenity[] amenities)
    {
        _amenities = amenities.ToList();
        return this;
    }

    public ApartmentTestDataBuilder WithAmenities(List<Amenity> amenities)
    {
        _amenities = amenities;
        return this;
    }

    public ApartmentTestDataBuilder WithAllAmenities()
    {
        _amenities = Enum.GetValues<Amenity>().ToList();
        return this;
    }

    public ApartmentTestDataBuilder WithBasicAmenities()
    {
        _amenities = new List<Amenity> { Amenity.WiFi, Amenity.Parking };
        return this;
    }

    public ApartmentTestDataBuilder WithLuxuryAmenities()
    {
        _amenities = new List<Amenity> 
        { 
            Amenity.WiFi, 
            Amenity.AirConditioning, 
            Amenity.Parking, 
            Amenity.SwimmingPoool, 
            Amenity.Gym, 
            Amenity.Spa,
            Amenity.MountainView 
        };
        return this;
    }

    public ApartmentTestDataBuilder WithLastBookedOnUtc(DateTime? lastBookedOnUtc)
    {
        _lastBookedOnUtc = lastBookedOnUtc;
        return this;
    }

    public Apartment Build()
    {
        var apartment = new Apartment(_id, _name, _description, _address, _price, _cleaningFee, _amenities);
        
        if (_lastBookedOnUtc.HasValue)
        {
            // Use reflection to set the internal property for testing
            var property = typeof(Apartment).GetProperty(nameof(Apartment.LastBookedOnUtc));
            property?.SetValue(apartment, _lastBookedOnUtc.Value);
        }

        return apartment;
    }

    /// <summary>
    /// Static factory method for fluent API
    /// </summary>
    public static ApartmentTestDataBuilder Create() => new();

    /// <summary>
    /// Creates a basic apartment with default values
    /// </summary>
    public static Apartment CreateDefault() => new ApartmentTestDataBuilder().Build();

    /// <summary>
    /// Creates a luxury apartment with high price and all amenities
    /// </summary>
    public static Apartment CreateLuxury() => new ApartmentTestDataBuilder()
        .WithName("Luxury Penthouse")
        .WithDescription("Stunning luxury penthouse with panoramic city views")
        .WithPrice(500.0m)
        .WithCleaningFee(100.0m)
        .WithLuxuryAmenities()
        .Build();

    /// <summary>
    /// Creates a budget apartment with low price and basic amenities
    /// </summary>
    public static Apartment CreateBudget() => new ApartmentTestDataBuilder()
        .WithName("Cozy Studio")
        .WithDescription("Affordable studio apartment perfect for short stays")
        .WithPrice(50.0m)
        .WithCleaningFee(25.0m)
        .WithBasicAmenities()
        .Build();

    /// <summary>
    /// Creates an apartment with no cleaning fee
    /// </summary>
    public static Apartment CreateWithoutCleaningFee() => new ApartmentTestDataBuilder()
        .WithNoCleaningFee()
        .Build();

    /// <summary>
    /// Creates an apartment in a specific location
    /// </summary>
    public static Apartment CreateInLocation(string country, string state, string city) => new ApartmentTestDataBuilder()
        .WithAddress(country, state, "12345", city, "123 Main Street")
        .Build();

    /// <summary>
    /// Creates an apartment with specific price
    /// </summary>
    public static Apartment CreateWithPrice(decimal priceAmount, Currency currency = Currency.Usd) => new ApartmentTestDataBuilder()
        .WithPrice(priceAmount, currency)
        .Build();
}
