using Bookify.Api.FunctionalTests.Infrastructure;
using Bookify.API.Controllers.Users;
using FluentAssertions;
using System.Net;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text;

namespace Bookify.Api.FunctionalTests.Security;

public class SecurityVulnerabilityTests : BaseFunctionalTest
{
    public SecurityVulnerabilityTests(FunctionalTestWebAppFactory factory) : base(factory)
    {
    }

    [Theory]
    [InlineData("'; DROP TABLE Users; --")]
    [InlineData("' OR '1'='1")]
    [InlineData("'; DELETE FROM Users WHERE '1'='1'; --")]
    [InlineData("' UNION SELECT * FROM Users --")]
    [InlineData("admin'--")]
    [InlineData("' OR 1=1 --")]
    [InlineData("'; EXEC xp_cmdshell('dir'); --")]
    public async Task RegisterUser_ShouldPreventSqlInjection_InEmailField(string sqlInjectionPayload)
    {
        // Arrange
        var request = new RegisterUserRequest(
            sqlInjectionPayload, // SQL injection in email
            "John",
            "Doe",
            "Password123!"
        );

        // Act
        var response = await HttpClient.PostAsJsonAsync("api/v1/users/register", request);

        // Assert
        // Should either reject the input (BadRequest) or handle it safely (OK)
        // Should NOT return a server error indicating SQL injection succeeded
        response.StatusCode.Should().BeOneOf(HttpStatusCode.BadRequest, HttpStatusCode.OK);
        response.StatusCode.Should().NotBe(HttpStatusCode.InternalServerError);
    }

    [Theory]
    [InlineData("'; DROP TABLE Users; --")]
    [InlineData("' OR '1'='1")]
    [InlineData("<script>alert('xss')</script>")]
    [InlineData("Robert'); DROP TABLE Students; --")]
    public async Task RegisterUser_ShouldPreventSqlInjection_InNameFields(string maliciousInput)
    {
        // Arrange
        var request = new RegisterUserRequest(
            "<EMAIL>",
            maliciousInput, // Malicious input in first name
            "Doe",
            "Password123!"
        );

        // Act
        var response = await HttpClient.PostAsJsonAsync("api/v1/users/register", request);

        // Assert
        response.StatusCode.Should().BeOneOf(HttpStatusCode.BadRequest, HttpStatusCode.OK);
        response.StatusCode.Should().NotBe(HttpStatusCode.InternalServerError);
    }

    [Theory]
    [InlineData("<script>alert('XSS')</script>")]
    [InlineData("<img src=x onerror=alert('XSS')>")]
    [InlineData("javascript:alert('XSS')")]
    [InlineData("<svg onload=alert('XSS')>")]
    [InlineData("'><script>alert('XSS')</script>")]
    [InlineData("\"><script>alert('XSS')</script>")]
    public async Task RegisterUser_ShouldPreventXSS_InInputFields(string xssPayload)
    {
        // Arrange
        var request = new RegisterUserRequest(
            "<EMAIL>",
            xssPayload, // XSS payload in first name
            "Doe",
            "Password123!"
        );

        // Act
        var response = await HttpClient.PostAsJsonAsync("api/v1/users/register", request);

        // Assert
        response.StatusCode.Should().BeOneOf(HttpStatusCode.BadRequest, HttpStatusCode.OK);
        
        if (response.StatusCode == HttpStatusCode.OK)
        {
            // If the request was accepted, verify the response doesn't contain unescaped script tags
            var responseContent = await response.Content.ReadAsStringAsync();
            responseContent.Should().NotContain("<script>");
            responseContent.Should().NotContain("javascript:");
            responseContent.Should().NotContain("onerror=");
        }
    }

    [Theory]
    [InlineData("../../../etc/passwd")]
    [InlineData("..\\..\\..\\windows\\system32\\config\\sam")]
    [InlineData("../../../../etc/shadow")]
    [InlineData("..%2F..%2F..%2Fetc%2Fpasswd")]
    [InlineData("....//....//....//etc/passwd")]
    public async Task RegisterUser_ShouldPreventPathTraversal_InInputFields(string pathTraversalPayload)
    {
        // Arrange
        var request = new RegisterUserRequest(
            "<EMAIL>",
            pathTraversalPayload, // Path traversal in first name
            "Doe",
            "Password123!"
        );

        // Act
        var response = await HttpClient.PostAsJsonAsync("api/v1/users/register", request);

        // Assert
        response.StatusCode.Should().BeOneOf(HttpStatusCode.BadRequest, HttpStatusCode.OK);
        response.StatusCode.Should().NotBe(HttpStatusCode.InternalServerError);
    }

    [Fact]
    public async Task API_ShouldPreventCSRF_OnStateChangingOperations()
    {
        // Arrange
        var userToken = await GetAccessToken();
        
        // Simulate a CSRF attack by making a request without proper CSRF protection
        // and with a suspicious origin
        HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", userToken);
        HttpClient.DefaultRequestHeaders.Add("Origin", "https://malicious-site.com");
        HttpClient.DefaultRequestHeaders.Add("Referer", "https://malicious-site.com/attack.html");

        var request = new RegisterUserRequest(
            "<EMAIL>",
            "CSRF",
            "Test",
            "Password123!"
        );

        // Act
        var response = await HttpClient.PostAsJsonAsync("api/v1/users/register", request);

        // Assert
        // The API should either:
        // 1. Reject the request due to CSRF protection (if implemented)
        // 2. Accept it but with proper validation (current behavior)
        // It should NOT be vulnerable to CSRF attacks
        response.StatusCode.Should().BeOneOf(
            HttpStatusCode.BadRequest, 
            HttpStatusCode.Forbidden, 
            HttpStatusCode.OK);
        
        // Clean up
        HttpClient.DefaultRequestHeaders.Clear();
    }

    [Fact]
    public async Task API_ShouldHaveSecurityHeaders()
    {
        // Arrange & Act
        var response = await HttpClient.GetAsync("api/v1/users/register");

        // Assert - Check for important security headers
        var headers = response.Headers;
        
        // Note: These assertions depend on the security headers being configured
        // Some might not be present in a test environment
        
        // X-Content-Type-Options should prevent MIME sniffing
        if (headers.Contains("X-Content-Type-Options"))
        {
            headers.GetValues("X-Content-Type-Options").Should().Contain("nosniff");
        }

        // X-Frame-Options should prevent clickjacking
        if (headers.Contains("X-Frame-Options"))
        {
            headers.GetValues("X-Frame-Options").Should().Contain(value => 
                value.Equals("DENY", StringComparison.OrdinalIgnoreCase) || 
                value.Equals("SAMEORIGIN", StringComparison.OrdinalIgnoreCase));
        }

        // X-XSS-Protection should be enabled
        if (headers.Contains("X-XSS-Protection"))
        {
            headers.GetValues("X-XSS-Protection").Should().Contain("1; mode=block");
        }
    }

    [Fact]
    public async Task API_ShouldPreventInformationDisclosure_InErrorMessages()
    {
        // Arrange - Send a request that will cause an error
        var invalidRequest = new RegisterUserRequest(
            "invalid-email",
            "",
            "",
            ""
        );

        // Act
        var response = await HttpClient.PostAsJsonAsync("api/v1/users/register", invalidRequest);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        
        var responseContent = await response.Content.ReadAsStringAsync();
        
        // Error messages should not contain sensitive information
        responseContent.Should().NotContain("ConnectionString");
        responseContent.Should().NotContain("Database");
        responseContent.Should().NotContain("Server");
        responseContent.Should().NotContain("Password");
        responseContent.Should().NotContain("Exception");
        responseContent.Should().NotContain("StackTrace");
        responseContent.Should().NotContain("at System.");
        responseContent.Should().NotContain("at Microsoft.");
    }

    [Fact]
    public async Task API_ShouldRateLimitRequests()
    {
        // Arrange - Make many requests quickly
        var tasks = new List<Task<HttpResponseMessage>>();
        
        for (int i = 0; i < 100; i++)
        {
            var request = new RegisterUserRequest(
                $"test{i}@test.com",
                "Test",
                "User",
                "Password123!"
            );
            
            tasks.Add(HttpClient.PostAsJsonAsync("api/v1/users/register", request));
        }

        // Act
        var responses = await Task.WhenAll(tasks);

        // Assert
        // If rate limiting is implemented, some requests should be rejected
        var tooManyRequestsResponses = responses.Count(r => r.StatusCode == HttpStatusCode.TooManyRequests);
        
        // Note: This test documents expected behavior
        // In a production system, rate limiting should be implemented
        // For now, we just verify the system doesn't crash under load
        responses.Should().AllSatisfy(response => 
            response.StatusCode.Should().BeOneOf(
                HttpStatusCode.OK, 
                HttpStatusCode.BadRequest, 
                HttpStatusCode.TooManyRequests,
                HttpStatusCode.Conflict)); // Conflict for duplicate emails
    }

    [Theory]
    [InlineData("AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA")]
    [InlineData("A")]
    public async Task API_ShouldValidateInputLength(string inputOfVariousLengths)
    {
        // Arrange
        var request = new RegisterUserRequest(
            "<EMAIL>",
            inputOfVariousLengths, // Very long or very short first name
            "Doe",
            "Password123!"
        );

        // Act
        var response = await HttpClient.PostAsJsonAsync("api/v1/users/register", request);

        // Assert
        // Should handle both very long and very short inputs gracefully
        response.StatusCode.Should().BeOneOf(HttpStatusCode.BadRequest, HttpStatusCode.OK);
        response.StatusCode.Should().NotBe(HttpStatusCode.InternalServerError);
    }

    [Fact]
    public async Task API_ShouldHandleMalformedJson()
    {
        // Arrange
        var malformedJsonPayloads = new[]
        {
            "{ malformed json }",
            "{ \"email\": \"<EMAIL>\", \"firstName\": }",
            "{ \"email\": \"<EMAIL>\", \"firstName\": \"John\", \"lastName\": \"Doe\", \"password\": \"Password123!\", }",
            "not json at all",
            "{ \"email\": \"<EMAIL>\", \"firstName\": \"John\", \"lastName\": \"Doe\", \"password\": \"Password123!\", \"extraField\": \"value\" }"
        };

        foreach (var payload in malformedJsonPayloads)
        {
            // Arrange
            var content = new StringContent(payload, Encoding.UTF8, "application/json");

            // Act
            var response = await HttpClient.PostAsync("api/v1/users/register", content);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest, 
                $"Malformed JSON should return BadRequest: {payload}");
        }
    }

    [Fact]
    public async Task API_ShouldPreventTimingAttacks_OnLogin()
    {
        // Arrange
        var existingUserEmail = "<EMAIL>";
        var nonExistentUserEmail = "<EMAIL>";
        var password = "Password123!";

        // Create an existing user
        await HttpClient.PostAsJsonAsync("api/v1/users/register", 
            new RegisterUserRequest(existingUserEmail, "Existing", "User", password));

        // Act - Measure response times for existing vs non-existent users
        var existingUserLoginRequest = new LogInUserRequest(existingUserEmail, "WrongPassword");
        var nonExistentUserLoginRequest = new LogInUserRequest(nonExistentUserEmail, "WrongPassword");

        var existingUserStart = DateTime.UtcNow;
        var existingUserResponse = await HttpClient.PostAsJsonAsync("api/v1/users/login", existingUserLoginRequest);
        var existingUserTime = DateTime.UtcNow - existingUserStart;

        var nonExistentUserStart = DateTime.UtcNow;
        var nonExistentUserResponse = await HttpClient.PostAsJsonAsync("api/v1/users/login", nonExistentUserLoginRequest);
        var nonExistentUserTime = DateTime.UtcNow - nonExistentUserStart;

        // Assert
        existingUserResponse.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
        nonExistentUserResponse.StatusCode.Should().Be(HttpStatusCode.Unauthorized);

        // The response times should be similar to prevent timing attacks
        // Allow for some variance due to system load
        var timeDifference = Math.Abs((existingUserTime - nonExistentUserTime).TotalMilliseconds);
        timeDifference.Should().BeLessThan(1000, "Response times should be similar to prevent timing attacks");
    }
}
