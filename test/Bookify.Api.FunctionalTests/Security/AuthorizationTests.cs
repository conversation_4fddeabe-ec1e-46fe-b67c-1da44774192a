using Bookify.Api.FunctionalTests.Infrastructure;
using Bookify.API.Controllers.Bookings;
using Bookify.API.Controllers.Users;
using FluentAssertions;
using System.Net;
using System.Net.Http.Headers;
using System.Net.Http.Json;

namespace Bookify.Api.FunctionalTests.Security;

public class AuthorizationTests : BaseFunctionalTest
{
    public AuthorizationTests(FunctionalTestWebAppFactory factory) : base(factory)
    {
    }

    [Fact]
    public async Task ProtectedEndpoints_ShouldRequireAuthentication()
    {
        // Arrange - Don't set authorization header
        HttpClient.DefaultRequestHeaders.Authorization = null;

        var protectedEndpoints = new[]
        {
            ("GET", "api/v1/users/me"),
            ("POST", "api/v1/bookings"),
            ("GET", $"api/v1/bookings/{Guid.NewGuid()}"),
            ("PUT", $"api/v1/bookings-enhanced/{Guid.NewGuid()}"),
            ("DELETE", $"api/v1/bookings-enhanced/{Guid.NewGuid()}")
        };

        foreach (var (method, endpoint) in protectedEndpoints)
        {
            // Act
            var response = method switch
            {
                "GET" => await HttpClient.GetAsync(endpoint),
                "POST" => await HttpClient.PostAsJsonAsync(endpoint, new { }),
                "PUT" => await HttpClient.PutAsJsonAsync(endpoint, new { }),
                "DELETE" => await HttpClient.DeleteAsync(endpoint),
                _ => throw new ArgumentException($"Unsupported method: {method}")
            };

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Unauthorized, 
                $"Endpoint {method} {endpoint} should require authentication");
        }
    }

    [Fact]
    public async Task PublicEndpoints_ShouldNotRequireAuthentication()
    {
        // Arrange - Don't set authorization header
        HttpClient.DefaultRequestHeaders.Authorization = null;

        var publicEndpoints = new[]
        {
            ("POST", "api/v1/users/register"),
            ("POST", "api/v1/users/login")
        };

        foreach (var (method, endpoint) in publicEndpoints)
        {
            // Arrange valid request data
            object requestData = endpoint switch
            {
                "api/v1/users/register" => new RegisterUserRequest("<EMAIL>", "Test", "User", "Password123!"),
                "api/v1/users/login" => new LogInUserRequest("<EMAIL>", "Password123!"),
                _ => new { }
            };

            // Act
            var response = method switch
            {
                "POST" => await HttpClient.PostAsJsonAsync(endpoint, requestData),
                _ => throw new ArgumentException($"Unsupported method: {method}")
            };

            // Assert
            response.StatusCode.Should().NotBe(HttpStatusCode.Unauthorized, 
                $"Endpoint {method} {endpoint} should not require authentication");
        }
    }

    [Fact]
    public async Task InvalidToken_ShouldReturnUnauthorized()
    {
        // Arrange
        var invalidTokens = new[]
        {
            "invalid-token",
            "Bearer invalid-token",
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid.signature",
            "",
            " ",
            "null"
        };

        foreach (var invalidToken in invalidTokens)
        {
            // Arrange
            HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", invalidToken);

            // Act
            var response = await HttpClient.GetAsync("api/v1/users/me");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Unauthorized, 
                $"Invalid token '{invalidToken}' should return Unauthorized");
        }

        // Clean up
        HttpClient.DefaultRequestHeaders.Authorization = null;
    }

    [Fact]
    public async Task ValidToken_ShouldAllowAccessToProtectedEndpoints()
    {
        // Arrange
        var userToken = await GetAccessToken();
        HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", userToken);

        // Act
        var response = await HttpClient.GetAsync("api/v1/users/me");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        // Clean up
        HttpClient.DefaultRequestHeaders.Authorization = null;
    }

    [Fact]
    public async Task ResourceBasedAuthorization_ShouldPreventAccessToOtherUsersResources()
    {
        // Arrange - Create two users
        var user1Email = "<EMAIL>";
        var user1Password = "Password123!";
        var user2Email = "<EMAIL>";
        var user2Password = "Password123!";

        // Register both users
        await HttpClient.PostAsJsonAsync("api/v1/users/register", 
            new RegisterUserRequest(user1Email, "User", "One", user1Password));
        await HttpClient.PostAsJsonAsync("api/v1/users/register", 
            new RegisterUserRequest(user2Email, "User", "Two", user2Password));

        // Get tokens for both users
        var user1LoginResponse = await HttpClient.PostAsJsonAsync("api/v1/users/login", 
            new LogInUserRequest(user1Email, user1Password));
        var user1Token = (await user1LoginResponse.Content.ReadFromJsonAsync<AccessTokenResponse>())!.AccessToken;

        var user2LoginResponse = await HttpClient.PostAsJsonAsync("api/v1/users/login", 
            new LogInUserRequest(user2Email, user2Password));
        var user2Token = (await user2LoginResponse.Content.ReadFromJsonAsync<AccessTokenResponse>())!.AccessToken;

        // User1 tries to access User2's profile (this should fail if implemented)
        HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", user1Token);
        
        // Act - Try to access another user's data
        // Note: In this case, /users/me always returns the current user's data from the token
        // So this test verifies that the endpoint uses the token's user ID, not a parameter
        var response = await HttpClient.GetAsync("api/v1/users/me");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var userProfile = await response.Content.ReadFromJsonAsync<UserResponse>();
        userProfile.Should().NotBeNull();
        userProfile!.Email.Should().Be(user1Email); // Should return User1's data, not User2's
        
        // Clean up
        HttpClient.DefaultRequestHeaders.Authorization = null;
    }

    [Fact]
    public async Task TokenExpiration_ShouldBeHandledCorrectly()
    {
        // Note: This test would require creating an expired token or waiting for expiration
        // For now, we test with a malformed token that simulates expiration
        
        // Arrange - Use a token that looks expired (this is a simulation)
        var expiredLookingToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2MDk0NTkyMDB9.invalid";
        HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", expiredLookingToken);

        // Act
        var response = await HttpClient.GetAsync("api/v1/users/me");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
        
        // Clean up
        HttpClient.DefaultRequestHeaders.Authorization = null;
    }

    [Fact]
    public async Task RoleBasedAuthorization_ShouldWorkCorrectly()
    {
        // Note: This test assumes role-based authorization is implemented
        // Currently, the system only has "Registered" role, so this test documents expected behavior
        
        // Arrange
        var userToken = await GetAccessToken();
        HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", userToken);

        // Act - Try to access an endpoint that might require specific roles
        // For now, all authenticated users can access these endpoints
        var response = await HttpClient.GetAsync("api/v1/users/me");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        // In the future, when admin endpoints are added, this test would verify:
        // - Regular users get 403 Forbidden for admin endpoints
        // - Admin users get 200 OK for admin endpoints
        
        // Clean up
        HttpClient.DefaultRequestHeaders.Authorization = null;
    }

    [Fact]
    public async Task ConcurrentRequests_ShouldMaintainAuthorization()
    {
        // Arrange
        var userToken = await GetAccessToken();
        
        var tasks = Enumerable.Range(0, 5).Select(async i =>
        {
            using var client = HttpClient;
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", userToken);
            return await client.GetAsync("api/v1/users/me");
        });

        // Act
        var responses = await Task.WhenAll(tasks);

        // Assert
        responses.Should().AllSatisfy(response => 
            response.StatusCode.Should().Be(HttpStatusCode.OK));
    }

    [Fact]
    public async Task AuthorizationHeader_ShouldBeCaseInsensitive()
    {
        // Arrange
        var userToken = await GetAccessToken();
        
        var headerVariations = new[]
        {
            ("Bearer", userToken),
            ("bearer", userToken),
            ("BEARER", userToken)
        };

        foreach (var (scheme, token) in headerVariations)
        {
            // Arrange
            HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(scheme, token);

            // Act
            var response = await HttpClient.GetAsync("api/v1/users/me");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK, 
                $"Authorization header with scheme '{scheme}' should work");
        }

        // Clean up
        HttpClient.DefaultRequestHeaders.Authorization = null;
    }

    [Fact]
    public async Task MultipleAuthorizationHeaders_ShouldBeHandledCorrectly()
    {
        // Arrange
        var userToken = await GetAccessToken();
        
        // Add multiple authorization headers (this should be handled gracefully)
        HttpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {userToken}");
        HttpClient.DefaultRequestHeaders.Add("Authorization", "Bearer invalid-token");

        // Act
        var response = await HttpClient.GetAsync("api/v1/users/me");

        // Assert
        // The behavior here depends on implementation - it might use the first header,
        // reject multiple headers, or handle it differently
        response.StatusCode.Should().BeOneOf(HttpStatusCode.OK, HttpStatusCode.Unauthorized, HttpStatusCode.BadRequest);
        
        // Clean up
        HttpClient.DefaultRequestHeaders.Clear();
    }

    [Fact]
    public async Task CrossOriginRequests_ShouldRespectAuthorizationPolicies()
    {
        // Arrange
        var userToken = await GetAccessToken();
        HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", userToken);
        
        // Add CORS headers to simulate cross-origin request
        HttpClient.DefaultRequestHeaders.Add("Origin", "https://malicious-site.com");

        // Act
        var response = await HttpClient.GetAsync("api/v1/users/me");

        // Assert
        // The authorization should still work regardless of origin
        // (CORS is handled separately from authorization)
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        // Clean up
        HttpClient.DefaultRequestHeaders.Clear();
    }
}
