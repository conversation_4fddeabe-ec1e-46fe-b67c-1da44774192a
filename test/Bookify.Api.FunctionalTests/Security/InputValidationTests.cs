using Bookify.Api.FunctionalTests.Infrastructure;
using Bookify.API.Controllers.Bookings;
using Bookify.API.Controllers.Users;
using FluentAssertions;
using System.Net;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;

namespace Bookify.Api.FunctionalTests.Security;

public class InputValidationTests : BaseFunctionalTest
{
    public InputValidationTests(FunctionalTestWebAppFactory factory) : base(factory)
    {
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData("invalid-email")]
    [InlineData("@domain.com")]
    [InlineData("user@")]
    [InlineData("user@.com")]
    [InlineData("<EMAIL>")]
    public async Task RegisterUser_ShouldReturnBadRequest_WhenEmailIsInvalid(string invalidEmail)
    {
        // Arrange
        var request = new RegisterUserRequest(invalidEmail, "John", "Doe", "Password123!");

        // Act
        var response = await HttpClient.PostAsJsonAsync("api/v1/users/register", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public async Task RegisterUser_ShouldReturnBadRequest_WhenFirstNameIsInvalid(string invalidFirstName)
    {
        // Arrange
        var request = new RegisterUserRequest("<EMAIL>", invalidFirstName, "Doe", "Password123!");

        // Act
        var response = await HttpClient.PostAsJsonAsync("api/v1/users/register", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public async Task RegisterUser_ShouldReturnBadRequest_WhenLastNameIsInvalid(string invalidLastName)
    {
        // Arrange
        var request = new RegisterUserRequest("<EMAIL>", "John", invalidLastName, "Password123!");

        // Act
        var response = await HttpClient.PostAsJsonAsync("api/v1/users/register", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData("123")]
    [InlineData("password")]
    [InlineData("PASSWORD")]
    [InlineData("Password")]
    [InlineData("12345678")]
    public async Task RegisterUser_ShouldReturnBadRequest_WhenPasswordIsWeak(string weakPassword)
    {
        // Arrange
        var request = new RegisterUserRequest("<EMAIL>", "John", "Doe", weakPassword);

        // Act
        var response = await HttpClient.PostAsJsonAsync("api/v1/users/register", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task RegisterUser_ShouldReturnBadRequest_WhenRequestBodyIsEmpty()
    {
        // Arrange
        var emptyContent = new StringContent("", Encoding.UTF8, "application/json");

        // Act
        var response = await HttpClient.PostAsync("api/v1/users/register", emptyContent);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task RegisterUser_ShouldReturnBadRequest_WhenRequestBodyIsInvalidJson()
    {
        // Arrange
        var invalidJsonContent = new StringContent("{ invalid json }", Encoding.UTF8, "application/json");

        // Act
        var response = await HttpClient.PostAsync("api/v1/users/register", invalidJsonContent);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task ReserveBooking_ShouldReturnBadRequest_WhenApartmentIdIsEmpty()
    {
        // Arrange
        var userToken = await GetAccessToken();
        HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", userToken);

        var request = new ReserveBookingRequest(
            Guid.Empty, // Invalid apartment ID
            new DateOnly(2024, 6, 1),
            new DateOnly(2024, 6, 10)
        );

        // Act
        var response = await HttpClient.PostAsJsonAsync("api/v1/bookings", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        
        // Clean up
        HttpClient.DefaultRequestHeaders.Authorization = null;
    }

    [Fact]
    public async Task ReserveBooking_ShouldReturnBadRequest_WhenEndDateIsBeforeStartDate()
    {
        // Arrange
        var userToken = await GetAccessToken();
        HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", userToken);

        var request = new ReserveBookingRequest(
            Guid.NewGuid(),
            new DateOnly(2024, 6, 10), // Start date after end date
            new DateOnly(2024, 6, 1)   // End date before start date
        );

        // Act
        var response = await HttpClient.PostAsJsonAsync("api/v1/bookings", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        
        // Clean up
        HttpClient.DefaultRequestHeaders.Authorization = null;
    }

    [Fact]
    public async Task ReserveBooking_ShouldReturnBadRequest_WhenStartDateIsInPast()
    {
        // Arrange
        var userToken = await GetAccessToken();
        HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", userToken);

        var request = new ReserveBookingRequest(
            Guid.NewGuid(),
            new DateOnly(2020, 1, 1), // Past date
            new DateOnly(2020, 1, 10)
        );

        // Act
        var response = await HttpClient.PostAsJsonAsync("api/v1/bookings", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        
        // Clean up
        HttpClient.DefaultRequestHeaders.Authorization = null;
    }

    [Theory]
    [InlineData("2024-13-01", "2024-13-10")] // Invalid month
    [InlineData("2024-02-30", "2024-02-31")] // Invalid day for February
    [InlineData("2024-04-31", "2024-05-01")] // Invalid day for April
    public async Task ReserveBooking_ShouldHandleInvalidDates(string startDateStr, string endDateStr)
    {
        // Arrange
        var userToken = await GetAccessToken();
        HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", userToken);

        // Create JSON manually with invalid dates
        var jsonContent = $@"{{
            ""apartmentId"": ""{Guid.NewGuid()}"",
            ""startDate"": ""{startDateStr}"",
            ""endDate"": ""{endDateStr}""
        }}";

        var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

        // Act
        var response = await HttpClient.PostAsync("api/v1/bookings", content);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        
        // Clean up
        HttpClient.DefaultRequestHeaders.Authorization = null;
    }

    [Fact]
    public async Task UpdateBooking_ShouldReturnBadRequest_WhenBookingIdIsEmpty()
    {
        // Arrange
        var userToken = await GetAccessToken();
        HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", userToken);

        var request = new UpdateBookingRequest(
            new DateOnly(2024, 7, 1),
            new DateOnly(2024, 7, 10)
        );

        // Act
        var response = await HttpClient.PutAsJsonAsync($"api/v1/bookings-enhanced/{Guid.Empty}", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        
        // Clean up
        HttpClient.DefaultRequestHeaders.Authorization = null;
    }

    [Fact]
    public async Task API_ShouldReturnBadRequest_WhenContentTypeIsNotJson()
    {
        // Arrange
        var userToken = await GetAccessToken();
        HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", userToken);

        var content = new StringContent("some text content", Encoding.UTF8, "text/plain");

        // Act
        var response = await HttpClient.PostAsync("api/v1/bookings", content);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        
        // Clean up
        HttpClient.DefaultRequestHeaders.Authorization = null;
    }

    [Fact]
    public async Task API_ShouldReturnBadRequest_WhenRequestBodyIsTooLarge()
    {
        // Arrange
        var userToken = await GetAccessToken();
        HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", userToken);

        // Create a very large JSON payload
        var largeString = new string('A', 1024 * 1024); // 1MB string
        var largeRequest = new RegisterUserRequest(
            "<EMAIL>",
            largeString, // Very large first name
            "Doe",
            "Password123!"
        );

        // Act
        var response = await HttpClient.PostAsJsonAsync("api/v1/users/register", largeRequest);

        // Assert
        response.StatusCode.Should().BeOneOf(HttpStatusCode.BadRequest, HttpStatusCode.RequestEntityTooLarge);
        
        // Clean up
        HttpClient.DefaultRequestHeaders.Authorization = null;
    }

    [Theory]
    [InlineData("<script>alert('xss')</script>")]
    [InlineData("'; DROP TABLE Users; --")]
    [InlineData("../../../etc/passwd")]
    [InlineData("%3Cscript%3Ealert('xss')%3C/script%3E")]
    public async Task RegisterUser_ShouldSanitizeInput_WhenMaliciousContentProvided(string maliciousInput)
    {
        // Arrange
        var request = new RegisterUserRequest(
            "<EMAIL>",
            maliciousInput, // Malicious first name
            "Doe",
            "Password123!"
        );

        // Act
        var response = await HttpClient.PostAsJsonAsync("api/v1/users/register", request);

        // Assert
        // The API should either reject the input (BadRequest) or sanitize it (OK)
        // It should NOT return a server error or allow the malicious content through
        response.StatusCode.Should().BeOneOf(HttpStatusCode.BadRequest, HttpStatusCode.OK);
        
        if (response.StatusCode == HttpStatusCode.OK)
        {
            // If the request was accepted, verify the malicious content was sanitized
            // This would require additional verification logic
        }
    }

    [Fact]
    public async Task API_ShouldReturnMethodNotAllowed_WhenUsingWrongHttpMethod()
    {
        // Arrange
        var userToken = await GetAccessToken();
        HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", userToken);

        // Act - Try to use GET on a POST endpoint
        var response = await HttpClient.GetAsync("api/v1/users/register");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.MethodNotAllowed);
        
        // Clean up
        HttpClient.DefaultRequestHeaders.Authorization = null;
    }

    [Fact]
    public async Task API_ShouldReturnNotFound_WhenEndpointDoesNotExist()
    {
        // Arrange
        var userToken = await GetAccessToken();
        HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", userToken);

        // Act
        var response = await HttpClient.GetAsync("api/v1/nonexistent-endpoint");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        
        // Clean up
        HttpClient.DefaultRequestHeaders.Authorization = null;
    }
}
