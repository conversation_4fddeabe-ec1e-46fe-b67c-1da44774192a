using Bookify.Api.FunctionalTests.Infrastructure;
using Bookify.API.Controllers.Bookings;
using Bookify.API.Controllers.Users;
using FluentAssertions;
using System.Net;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;

namespace Bookify.Api.FunctionalTests.ErrorHandling;

public class ApiErrorHandlingTests : BaseFunctionalTest
{
    public ApiErrorHandlingTests(FunctionalTestWebAppFactory factory) : base(factory)
    {
    }

    [Fact]
    public async Task API_ShouldReturn400_WhenRequestBodyIsInvalid()
    {
        // Arrange
        var invalidJsonContent = new StringContent("{ invalid json }", Encoding.UTF8, "application/json");

        // Act
        var response = await HttpClient.PostAsync("api/v1/users/register", invalidJsonContent);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        
        var responseContent = await response.Content.ReadAsStringAsync();
        responseContent.Should().NotBeNullOrEmpty();
        responseContent.Should().NotContain("Exception");
        responseContent.Should().NotContain("StackTrace");
    }

    [Fact]
    public async Task API_ShouldReturn400_WhenRequiredFieldsAreMissing()
    {
        // Arrange
        var incompleteRequest = new
        {
            email = "<EMAIL>"
            // Missing firstName, lastName, password
        };

        // Act
        var response = await HttpClient.PostAsJsonAsync("api/v1/users/register", incompleteRequest);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        
        var responseContent = await response.Content.ReadAsStringAsync();
        responseContent.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task API_ShouldReturn401_WhenAuthenticationFails()
    {
        // Arrange
        var loginRequest = new LogInUserRequest("<EMAIL>", "wrongpassword");

        // Act
        var response = await HttpClient.PostAsJsonAsync("api/v1/users/login", loginRequest);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
        
        var responseContent = await response.Content.ReadAsStringAsync();
        responseContent.Should().NotContain("password");
        responseContent.Should().NotContain("hash");
        responseContent.Should().NotContain("salt");
    }

    [Fact]
    public async Task API_ShouldReturn401_WhenTokenIsInvalid()
    {
        // Arrange
        HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", "invalid-token");

        // Act
        var response = await HttpClient.GetAsync("api/v1/users/me");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
        
        // Clean up
        HttpClient.DefaultRequestHeaders.Authorization = null;
    }

    [Fact]
    public async Task API_ShouldReturn404_WhenResourceNotFound()
    {
        // Arrange
        var userToken = await GetAccessToken();
        HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", userToken);
        
        var nonExistentBookingId = Guid.NewGuid();

        // Act
        var response = await HttpClient.GetAsync($"api/v1/bookings/{nonExistentBookingId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        
        var responseContent = await response.Content.ReadAsStringAsync();
        responseContent.Should().NotContain("Exception");
        responseContent.Should().NotContain("StackTrace");
        
        // Clean up
        HttpClient.DefaultRequestHeaders.Authorization = null;
    }

    [Fact]
    public async Task API_ShouldReturn404_WhenEndpointDoesNotExist()
    {
        // Act
        var response = await HttpClient.GetAsync("api/v1/nonexistent-endpoint");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Fact]
    public async Task API_ShouldReturn405_WhenHttpMethodNotAllowed()
    {
        // Act - Try to use GET on a POST endpoint
        var response = await HttpClient.GetAsync("api/v1/users/register");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.MethodNotAllowed);
        
        // Should include allowed methods in response
        response.Headers.Should().ContainKey("Allow");
    }

    [Fact]
    public async Task API_ShouldReturn409_WhenResourceConflictOccurs()
    {
        // Arrange - Create a user first
        var email = "<EMAIL>";
        var registerRequest = new RegisterUserRequest(email, "Test", "User", "Password123!");
        
        var firstResponse = await HttpClient.PostAsJsonAsync("api/v1/users/register", registerRequest);
        firstResponse.StatusCode.Should().Be(HttpStatusCode.OK);

        // Act - Try to create the same user again
        var secondResponse = await HttpClient.PostAsJsonAsync("api/v1/users/register", registerRequest);

        // Assert
        secondResponse.StatusCode.Should().Be(HttpStatusCode.Conflict);
        
        var responseContent = await secondResponse.Content.ReadAsStringAsync();
        responseContent.Should().NotContain("Exception");
        responseContent.Should().NotContain("StackTrace");
    }

    [Fact]
    public async Task API_ShouldReturn415_WhenContentTypeIsUnsupported()
    {
        // Arrange
        var xmlContent = new StringContent("<xml>data</xml>", Encoding.UTF8, "application/xml");

        // Act
        var response = await HttpClient.PostAsync("api/v1/users/register", xmlContent);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.UnsupportedMediaType);
    }

    [Fact]
    public async Task API_ShouldReturn422_WhenBusinessRuleViolated()
    {
        // Arrange
        var userToken = await GetAccessToken();
        HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", userToken);

        // Try to create a booking with end date before start date
        var invalidBookingRequest = new ReserveBookingRequest(
            Guid.NewGuid(),
            new DateOnly(2024, 6, 10), // Start date
            new DateOnly(2024, 6, 1)   // End date (before start date)
        );

        // Act
        var response = await HttpClient.PostAsJsonAsync("api/v1/bookings", invalidBookingRequest);

        // Assert
        response.StatusCode.Should().BeOneOf(HttpStatusCode.BadRequest, HttpStatusCode.UnprocessableEntity);
        
        // Clean up
        HttpClient.DefaultRequestHeaders.Authorization = null;
    }

    [Fact]
    public async Task API_ShouldReturn500_WhenUnhandledExceptionOccurs()
    {
        // Note: This test is difficult to implement without actually causing a server error
        // In a real scenario, you might temporarily break something to test error handling
        
        // For now, we document the expected behavior:
        // - Unhandled exceptions should return 500 Internal Server Error
        // - Error details should not be exposed to clients in production
        // - Errors should be logged for debugging
        
        // This test serves as documentation for the expected behavior
        true.Should().BeTrue("Unhandled exception handling should return 500 without exposing details");
    }

    [Fact]
    public async Task API_ShouldNotExposeInternalDetails_InErrorResponses()
    {
        // Arrange
        var invalidRequest = new RegisterUserRequest("invalid-email", "", "", "");

        // Act
        var response = await HttpClient.PostAsJsonAsync("api/v1/users/register", invalidRequest);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        
        var responseContent = await response.Content.ReadAsStringAsync();
        
        // Should not contain internal implementation details
        responseContent.Should().NotContain("ConnectionString");
        responseContent.Should().NotContain("Database");
        responseContent.Should().NotContain("Server");
        responseContent.Should().NotContain("Exception");
        responseContent.Should().NotContain("StackTrace");
        responseContent.Should().NotContain("at System.");
        responseContent.Should().NotContain("at Microsoft.");
        responseContent.Should().NotContain("Inner Exception");
        responseContent.Should().NotContain("Source:");
    }

    [Fact]
    public async Task API_ShouldReturnConsistentErrorFormat()
    {
        // Arrange
        var invalidRequests = new[]
        {
            ("api/v1/users/register", new RegisterUserRequest("invalid-email", "Test", "User", "Password123!")),
            ("api/v1/users/login", new LogInUserRequest("<EMAIL>", "wrongpassword"))
        };

        foreach (var (endpoint, request) in invalidRequests)
        {
            // Act
            var response = await HttpClient.PostAsJsonAsync(endpoint, request);

            // Assert
            response.StatusCode.Should().BeOneOf(HttpStatusCode.BadRequest, HttpStatusCode.Unauthorized);
            
            var responseContent = await response.Content.ReadAsStringAsync();
            responseContent.Should().NotBeNullOrEmpty();
            
            // Try to parse as JSON to ensure consistent format
            try
            {
                var jsonDocument = JsonDocument.Parse(responseContent);
                jsonDocument.Should().NotBeNull("Error responses should be valid JSON");
            }
            catch (JsonException)
            {
                // If it's not JSON, it should at least be a meaningful error message
                responseContent.Should().NotContain("Exception");
            }
        }
    }

    [Fact]
    public async Task API_ShouldHandleLargePayloads()
    {
        // Arrange - Create a very large request
        var largeString = new string('A', 1024 * 1024); // 1MB string
        var largeRequest = new RegisterUserRequest(
            "<EMAIL>",
            largeString,
            "User",
            "Password123!"
        );

        // Act
        var response = await HttpClient.PostAsJsonAsync("api/v1/users/register", largeRequest);

        // Assert
        response.StatusCode.Should().BeOneOf(
            HttpStatusCode.BadRequest, 
            HttpStatusCode.RequestEntityTooLarge,
            HttpStatusCode.PayloadTooLarge);
    }

    [Fact]
    public async Task API_ShouldHandleSpecialCharacters_InInput()
    {
        // Arrange
        var specialCharacters = "!@#$%^&*()_+-=[]{}|;':\",./<>?`~";
        var request = new RegisterUserRequest(
            "<EMAIL>",
            specialCharacters,
            "User",
            "Password123!"
        );

        // Act
        var response = await HttpClient.PostAsJsonAsync("api/v1/users/register", request);

        // Assert
        response.StatusCode.Should().BeOneOf(HttpStatusCode.BadRequest, HttpStatusCode.OK);
        response.StatusCode.Should().NotBe(HttpStatusCode.InternalServerError);
    }

    [Fact]
    public async Task API_ShouldHandleUnicodeCharacters_InInput()
    {
        // Arrange
        var unicodeString = "José María Azañón 中文 العربية русский 🚀";
        var request = new RegisterUserRequest(
            "<EMAIL>",
            unicodeString,
            "User",
            "Password123!"
        );

        // Act
        var response = await HttpClient.PostAsJsonAsync("api/v1/users/register", request);

        // Assert
        response.StatusCode.Should().BeOneOf(HttpStatusCode.BadRequest, HttpStatusCode.OK);
        response.StatusCode.Should().NotBe(HttpStatusCode.InternalServerError);
    }

    [Fact]
    public async Task API_ShouldHandleConcurrentRequests_Gracefully()
    {
        // Arrange
        var tasks = new List<Task<HttpResponseMessage>>();
        
        for (int i = 0; i < 10; i++)
        {
            var request = new RegisterUserRequest(
                $"concurrent{i}@test.com",
                "Test",
                "User",
                "Password123!"
            );
            
            tasks.Add(HttpClient.PostAsJsonAsync("api/v1/users/register", request));
        }

        // Act
        var responses = await Task.WhenAll(tasks);

        // Assert
        responses.Should().AllSatisfy(response => 
        {
            response.StatusCode.Should().BeOneOf(
                HttpStatusCode.OK, 
                HttpStatusCode.BadRequest,
                HttpStatusCode.Conflict);
            response.StatusCode.Should().NotBe(HttpStatusCode.InternalServerError);
        });
    }

    [Fact]
    public async Task API_ShouldProvideHelpfulErrorMessages()
    {
        // Arrange
        var invalidRequest = new RegisterUserRequest("invalid-email", "", "", "weak");

        // Act
        var response = await HttpClient.PostAsJsonAsync("api/v1/users/register", invalidRequest);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        
        var responseContent = await response.Content.ReadAsStringAsync();
        responseContent.Should().NotBeNullOrEmpty();
        
        // Error message should be helpful but not expose internal details
        responseContent.Should().NotContain("null reference");
        responseContent.Should().NotContain("object reference");
        responseContent.Should().NotContain("index out of range");
    }

    [Fact]
    public async Task API_ShouldHandleTimeout_Gracefully()
    {
        // Note: This test is difficult to implement without actually causing timeouts
        // In a real scenario, you might use a test endpoint that simulates long processing
        
        // For now, we document the expected behavior:
        // - Long-running requests should timeout gracefully
        // - Should return appropriate timeout status codes
        // - Should not leave resources in inconsistent state
        
        // This test serves as documentation for the expected behavior
        true.Should().BeTrue("Timeout handling should be implemented");
    }
}
