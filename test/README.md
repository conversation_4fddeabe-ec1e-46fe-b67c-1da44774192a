# Bookify Test Suite

This document provides an overview of the comprehensive test suite for the Bookify application, including test structure, patterns, and best practices.

## Test Structure

The test suite is organized into multiple layers following the Clean Architecture pattern:

### 1. Domain Unit Tests (`Bookify.Domain.UnitTests`)
- **Purpose**: Test domain entities, value objects, and business logic in isolation
- **Key Features**:
  - Comprehensive entity behavior testing
  - Value object validation and equality testing
  - Domain event verification
  - Business rule enforcement testing

#### Key Test Files:
- `Bookings/BookingTests.cs` - Complete booking lifecycle testing
- `Users/UserTests.cs` - User entity and role testing
- `Users/EmailTests.cs` - Email value object validation
- `Shared/MoneyTests.cs` - Money value object operations
- `Shared/DateRangeTests.cs` - Date range validation and operations

#### Test Builders:
- `TestBuilders/BookingTestDataBuilder.cs` - Fluent API for creating test bookings
- `TestBuilders/ApartmentTestDataBuilder.cs` - Fluent API for creating test apartments
- `TestBuilders/UserTestDataBuilder.cs` - Fluent API for creating test users

### 2. Application Integration Tests (`Bookify.Application.IntegrationTests`)
- **Purpose**: Test application handlers with real database interactions
- **Key Features**:
  - End-to-end command and query testing
  - Database transaction testing
  - Cross-cutting concern validation
  - Error handling verification

#### Key Test Files:
- `Bookings/ReserveBookingTests.cs` - Booking reservation integration tests
- `Bookings/ConfirmBookingTests.cs` - Booking confirmation workflow tests
- `Users/RegisterUserTests.cs` - User registration integration tests
- `Apartments/SearchApartmentsTests.cs` - Apartment search functionality tests
- `ErrorHandling/ErrorHandlingTests.cs` - Comprehensive error scenario testing
- `Performance/PerformanceTests.cs` - Performance and load testing

### 3. API Functional Tests (`Bookify.Api.FunctionalTests`)
- **Purpose**: Test HTTP endpoints end-to-end with authentication
- **Key Features**:
  - Complete HTTP request/response testing
  - Authentication and authorization testing
  - Input validation testing
  - Security vulnerability testing

#### Key Test Files:
- `Bookings/AuthorizationBehaviorTests.cs` - Booking authorization tests
- `Bookings/CrossUserAccessTests.cs` - Cross-user access prevention tests
- `Security/InputValidationTests.cs` - Input validation and sanitization tests
- `Security/AuthorizationTests.cs` - Comprehensive authorization tests
- `Security/SecurityVulnerabilityTests.cs` - Security vulnerability tests
- `ErrorHandling/ApiErrorHandlingTests.cs` - API error handling tests

### 4. Architecture Tests (`Bookify.ArchitectureTests`)
- **Purpose**: Enforce architectural constraints and dependencies
- **Key Features**:
  - Layer dependency validation
  - Naming convention enforcement
  - Architecture pattern compliance

## Test Utilities and Helpers

### Custom Assertions (`TestUtilities/CustomAssertions.cs`)
- Domain event assertions
- Result pattern assertions
- Entity-specific assertions
- Fluent assertion extensions

### Test Data Factory (`TestUtilities/TestDataFactory.cs`)
- Predefined test scenarios
- Common test data patterns
- Realistic test data generation

### Test Helpers (`TestUtilities/TestHelpers.cs`)
- Date/time utilities
- Money creation helpers
- Domain event testing utilities
- Reflection helpers for testing private members

### Integration Test Helpers (`Infrastructure/IntegrationTestHelpers.cs`)
- Database entity creation helpers
- Common test scenario builders
- Test data cleanup utilities

## Test Patterns and Best Practices

### 1. Test Data Builders
```csharp
var booking = BookingTestDataBuilder.Create()
    .WithApartment(apartment)
    .WithUser(user)
    .WithFutureDates()
    .BuildConfirmed();
```

### 2. Custom Assertions
```csharp
booking.Should().BeInStatus(BookingStatus.Confirmed);
user.Should().HaveRole(Role.Registered);
result.ShouldBeSuccess();
```

### 3. Domain Event Testing
```csharp
var domainEvent = booking.ShouldHavePublishedDomainEvent<BookingReservedDomainEvent>();
domainEvent.BookingId.Should().Be(booking.Id);
```

### 4. Error Scenario Testing
```csharp
result.ShouldBeFailure(BookingErrors.NotFound);
```

## Security Testing

### Input Validation
- SQL injection prevention
- XSS attack prevention
- Path traversal prevention
- Input sanitization verification

### Authorization Testing
- Cross-user access prevention
- Token validation
- Role-based access control
- Resource-based authorization

### Security Headers
- CSRF protection verification
- Security header validation
- Information disclosure prevention

## Performance Testing

### Load Testing
- Concurrent user operations
- Database performance under load
- Memory usage monitoring
- Response time validation

### Scalability Testing
- Large dataset handling
- Search performance with volume
- Concurrent booking scenarios

## Running Tests

### All Tests
```bash
dotnet test
```

### Specific Test Project
```bash
dotnet test test/Bookify.Domain.UnitTests
dotnet test test/Bookify.Application.IntegrationTests
dotnet test test/Bookify.Api.FunctionalTests
```

### Test Categories
```bash
# Unit tests only
dotnet test --filter Category=Unit

# Integration tests only
dotnet test --filter Category=Integration

# Performance tests only
dotnet test --filter Category=Performance
```

## Test Coverage

The test suite provides comprehensive coverage across:

- **Domain Logic**: 95%+ coverage of business rules and entity behavior
- **Application Layer**: Complete command/query handler testing
- **API Layer**: Full endpoint testing with security scenarios
- **Error Handling**: Comprehensive error scenario coverage
- **Security**: Extensive security vulnerability testing

## Continuous Integration

Tests are designed to run in CI/CD pipelines with:
- Isolated test databases
- Parallel test execution
- Deterministic test data
- Environment-independent assertions

## Best Practices Followed

1. **Arrange-Act-Assert Pattern**: Clear test structure
2. **Test Isolation**: Each test is independent
3. **Meaningful Names**: Descriptive test method names
4. **Single Responsibility**: One assertion per test concept
5. **Fast Execution**: Optimized for quick feedback
6. **Maintainable**: Easy to update as code evolves
7. **Comprehensive**: Cover happy path, edge cases, and error scenarios

## Contributing to Tests

When adding new features:

1. **Add Domain Tests**: Test business logic in isolation
2. **Add Integration Tests**: Test with real dependencies
3. **Add API Tests**: Test HTTP endpoints
4. **Add Security Tests**: Test authorization and validation
5. **Update Test Builders**: Extend builders for new scenarios
6. **Document Test Scenarios**: Update this README

## Test Data Management

- Use test builders for consistent data creation
- Leverage test data factory for common scenarios
- Clean up test data appropriately
- Use realistic but safe test data
- Avoid hardcoded values where possible

This comprehensive test suite ensures the Bookify application is robust, secure, and maintainable while providing fast feedback during development.
