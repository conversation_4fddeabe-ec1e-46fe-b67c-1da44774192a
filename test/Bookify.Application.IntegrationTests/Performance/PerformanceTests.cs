using Bookify.Application.Apartments.SearchApartments;
using Bookify.Application.Bookings.ReserveBooking;
using Bookify.Application.IntegrationTests.Infrastructure;
using Bookify.Application.Users.RegisterUser;
using FluentAssertions;
using System.Diagnostics;

namespace Bookify.Application.IntegrationTests.Performance;

public class PerformanceTests : BaseIntegrationTest
{
    public PerformanceTests(IntegrationTestWebAppFactory webAppFactory) : base(webAppFactory)
    {
    }

    [Fact]
    public async Task RegisterUser_ShouldCompleteWithinReasonableTime()
    {
        // Arrange
        var command = new RegisterUserCommand(
            "<EMAIL>",
            "Performance",
            "Test",
            "Password123!"
        );

        var stopwatch = Stopwatch.StartNew();

        // Act
        var result = await Sender.Send(command);

        // Assert
        stopwatch.Stop();
        result.IsSuccess.Should().BeTrue();
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000, "User registration should complete within 5 seconds");
    }

    [Fact]
    public async Task ReserveBooking_ShouldCompleteWithinReasonableTime()
    {
        // Arrange
        var user = await IntegrationTestHelpers.CreateAndSaveUserAsync(DbContext);
        var apartment = await IntegrationTestHelpers.CreateAndSaveApartmentAsync(DbContext);
        
        var command = new ReserveBookingCommand(
            apartment.Id.Value,
            user.Id.Value,
            new DateOnly(2024, 6, 1),
            new DateOnly(2024, 6, 10)
        );

        var stopwatch = Stopwatch.StartNew();

        // Act
        var result = await Sender.Send(command);

        // Assert
        stopwatch.Stop();
        result.IsSuccess.Should().BeTrue();
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(3000, "Booking reservation should complete within 3 seconds");
    }

    [Fact]
    public async Task SearchApartments_ShouldCompleteWithinReasonableTime()
    {
        // Arrange
        // Create multiple apartments for a more realistic search scenario
        var apartments = await IntegrationTestHelpers.CreateVariedApartmentsAsync(DbContext);
        
        var query = new SearchApartmentsQuery(
            new DateOnly(2024, 6, 1),
            new DateOnly(2024, 6, 10)
        );

        var stopwatch = Stopwatch.StartNew();

        // Act
        var result = await Sender.Send(query);

        // Assert
        stopwatch.Stop();
        result.IsSuccess.Should().BeTrue();
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(2000, "Apartment search should complete within 2 seconds");
    }

    [Fact]
    public async Task ConcurrentUserRegistrations_ShouldHandleLoadEfficiently()
    {
        // Arrange
        const int concurrentUsers = 10;
        var tasks = new List<Task<Result<Guid>>>();

        var stopwatch = Stopwatch.StartNew();

        // Act
        for (int i = 0; i < concurrentUsers; i++)
        {
            var command = new RegisterUserCommand(
                $"concurrent{i}@test.com",
                $"User{i}",
                "Test",
                "Password123!"
            );
            
            tasks.Add(Sender.Send(command));
        }

        var results = await Task.WhenAll(tasks);

        // Assert
        stopwatch.Stop();
        
        results.Should().AllSatisfy(result => result.IsSuccess.Should().BeTrue());
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(10000, 
            $"Concurrent registration of {concurrentUsers} users should complete within 10 seconds");
        
        // Calculate average time per registration
        var averageTimePerRegistration = stopwatch.ElapsedMilliseconds / (double)concurrentUsers;
        averageTimePerRegistration.Should().BeLessThan(2000, 
            "Average time per registration should be less than 2 seconds");
    }

    [Fact]
    public async Task ConcurrentBookingReservations_ShouldHandleLoadEfficiently()
    {
        // Arrange
        const int concurrentBookings = 5;
        var users = await IntegrationTestHelpers.CreateMultipleUsersAsync(DbContext, concurrentBookings);
        var apartments = await IntegrationTestHelpers.CreateVariedApartmentsAsync(DbContext);
        
        var tasks = new List<Task<Result<Guid>>>();
        var stopwatch = Stopwatch.StartNew();

        // Act
        for (int i = 0; i < concurrentBookings; i++)
        {
            var command = new ReserveBookingCommand(
                apartments[i % apartments.Count].Id.Value,
                users[i].Id.Value,
                new DateOnly(2024, 6, 1 + i), // Different dates to avoid conflicts
                new DateOnly(2024, 6, 10 + i)
            );
            
            tasks.Add(Sender.Send(command));
        }

        var results = await Task.WhenAll(tasks);

        // Assert
        stopwatch.Stop();
        
        results.Should().AllSatisfy(result => result.IsSuccess.Should().BeTrue());
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(15000, 
            $"Concurrent reservation of {concurrentBookings} bookings should complete within 15 seconds");
    }

    [Fact]
    public async Task SearchApartments_ShouldScaleWithDataVolume()
    {
        // Arrange
        // Create a larger number of apartments to test search performance with more data
        const int apartmentCount = 50;
        var apartmentTasks = new List<Task>();

        for (int i = 0; i < apartmentCount; i++)
        {
            apartmentTasks.Add(IntegrationTestHelpers.CreateAndSaveApartmentAsync(
                DbContext, 
                $"Performance Test Apartment {i}",
                50m + (i * 10m) // Varying prices
            ));
        }

        await Task.WhenAll(apartmentTasks);

        var query = new SearchApartmentsQuery(
            new DateOnly(2024, 6, 1),
            new DateOnly(2024, 6, 10)
        );

        var stopwatch = Stopwatch.StartNew();

        // Act
        var result = await Sender.Send(query);

        // Assert
        stopwatch.Stop();
        
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().HaveCountGreaterOrEqualTo(apartmentCount);
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000, 
            $"Search with {apartmentCount} apartments should complete within 5 seconds");
    }

    [Fact]
    public async Task DatabaseOperations_ShouldMaintainPerformanceUnderLoad()
    {
        // Arrange
        const int operationCount = 20;
        var operations = new List<Task>();
        var stopwatch = Stopwatch.StartNew();

        // Act - Mix different types of operations
        for (int i = 0; i < operationCount; i++)
        {
            if (i % 3 == 0)
            {
                // User registration
                var registerCommand = new RegisterUserCommand(
                    $"load{i}@test.com",
                    $"Load{i}",
                    "Test",
                    "Password123!"
                );
                operations.Add(Sender.Send(registerCommand));
            }
            else if (i % 3 == 1)
            {
                // Apartment search
                var searchQuery = new SearchApartmentsQuery(
                    new DateOnly(2024, 6, 1),
                    new DateOnly(2024, 6, 10)
                );
                operations.Add(Sender.Send(searchQuery));
            }
            else
            {
                // Create apartment and booking (more complex operation)
                operations.Add(CreateBookingScenario(i));
            }
        }

        await Task.WhenAll(operations);

        // Assert
        stopwatch.Stop();
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(30000, 
            $"Mixed load of {operationCount} operations should complete within 30 seconds");
    }

    [Fact]
    public async Task MemoryUsage_ShouldRemainStable_DuringOperations()
    {
        // Arrange
        var initialMemory = GC.GetTotalMemory(true);

        // Act - Perform multiple operations
        for (int i = 0; i < 10; i++)
        {
            var user = await IntegrationTestHelpers.CreateAndSaveUserAsync(DbContext, $"memory{i}@test.com");
            var apartment = await IntegrationTestHelpers.CreateAndSaveApartmentAsync(DbContext, $"Memory Apartment {i}");
            
            var command = new ReserveBookingCommand(
                apartment.Id.Value,
                user.Id.Value,
                new DateOnly(2024, 6, 1 + i),
                new DateOnly(2024, 6, 10 + i)
            );
            
            var result = await Sender.Send(command);
            result.IsSuccess.Should().BeTrue();
        }

        // Force garbage collection
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();

        // Assert
        var finalMemory = GC.GetTotalMemory(false);
        var memoryIncrease = finalMemory - initialMemory;
        
        // Memory increase should be reasonable (less than 50MB for this test)
        memoryIncrease.Should().BeLessThan(50 * 1024 * 1024, 
            "Memory usage should not increase excessively during operations");
    }

    private async Task CreateBookingScenario(int index)
    {
        var user = await IntegrationTestHelpers.CreateAndSaveUserAsync(DbContext, $"scenario{index}@test.com");
        var apartment = await IntegrationTestHelpers.CreateAndSaveApartmentAsync(DbContext, $"Scenario Apartment {index}");
        
        var command = new ReserveBookingCommand(
            apartment.Id.Value,
            user.Id.Value,
            new DateOnly(2024, 7, 1 + index),
            new DateOnly(2024, 7, 10 + index)
        );
        
        var result = await Sender.Send(command);
        result.IsSuccess.Should().BeTrue();
    }
}
