using Bookify.Application.IntegrationTests.Infrastructure;
using Bookify.Application.Users.RegisterUser;
using Bookify.Domain.Entities.Users;
using Bookify.Domain.Entities.Users.ValueObjects;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;

namespace Bookify.Application.IntegrationTests.Users;

public class RegisterUserTests : BaseIntegrationTest
{
    public RegisterUserTests(IntegrationTestWebAppFactory webAppFactory) : base(webAppFactory)
    {
    }

    [Fact]
    public async Task RegisterUser_ShouldReturnSuccess_WhenValidDataProvided()
    {
        // Arrange
        var command = new RegisterUserCommand(
            "<EMAIL>",
            "<PERSON>",
            "Doe",
            "SecurePassword123!");

        // Act
        var result = await Sender.Send(command);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().NotBe(Guid.Empty);

        // Verify user was saved to database
        var user = await DbContext.Users
            .FirstOrDefaultAsync(u => u.Id == new UserId(result.Value));

        user.Should().NotBeNull();
        user!.FirstName.Should().Be("John");
        user.LastName.Should().Be("Doe");
        user.Email.Value.Should().Be("<EMAIL>");
        user.Roles.Should().Contain(Role.Registered);
    }

    [Fact]
    public async Task RegisterUser_ShouldReturnFailure_WhenEmailAlreadyExists()
    {
        // Arrange
        var existingUser = User.Create("Existing", "User", new Email("<EMAIL>"));
        DbContext.Users.Add(existingUser);
        await DbContext.SaveChangesAsync();

        var command = new RegisterUserCommand(
            "<EMAIL>",
            "New",
            "User",
            "SecurePassword123!");

        // Act
        var result = await Sender.Send(command);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(UserErrors.EmailNotUnique);
    }

    [Theory]
    [InlineData("", "John", "Doe", "Password123!")]
    [InlineData("invalid-email", "John", "Doe", "Password123!")]
    [InlineData("<EMAIL>", "", "Doe", "Password123!")]
    [InlineData("<EMAIL>", "John", "", "Password123!")]
    [InlineData("<EMAIL>", "John", "Doe", "")]
    public async Task RegisterUser_ShouldReturnFailure_WhenInvalidDataProvided(
        string email, string firstName, string lastName, string password)
    {
        // Arrange
        var command = new RegisterUserCommand(email, firstName, lastName, password);

        // Act
        var result = await Sender.Send(command);

        // Assert
        result.IsFailure.Should().BeTrue();
        // The specific error would depend on validation implementation
    }

    [Fact]
    public async Task RegisterUser_ShouldNormalizeEmail()
    {
        // Arrange
        var command = new RegisterUserCommand(
            "<EMAIL>",
            "John",
            "Doe",
            "SecurePassword123!");

        // Act
        var result = await Sender.Send(command);

        // Assert
        result.IsSuccess.Should().BeTrue();

        var user = await DbContext.Users
            .FirstOrDefaultAsync(u => u.Id == new UserId(result.Value));

        user.Should().NotBeNull();
        user!.Email.Value.Should().Be("<EMAIL>"); // Should be normalized to lowercase
    }

    [Fact]
    public async Task RegisterUser_ShouldCreateUserWithRegisteredRole()
    {
        // Arrange
        var command = new RegisterUserCommand(
            "<EMAIL>",
            "John",
            "Doe",
            "SecurePassword123!");

        // Act
        var result = await Sender.Send(command);

        // Assert
        result.IsSuccess.Should().BeTrue();

        var user = await DbContext.Users
            .Include(u => u.Roles)
            .FirstOrDefaultAsync(u => u.Id == new UserId(result.Value));

        user.Should().NotBeNull();
        user!.Roles.Should().HaveCount(1);
        user.Roles.Should().Contain(Role.Registered);
    }

    [Fact]
    public async Task RegisterUser_ShouldRaiseDomainEvent()
    {
        // Arrange
        var command = new RegisterUserCommand(
            "<EMAIL>",
            "John",
            "Doe",
            "SecurePassword123!");

        // Act
        var result = await Sender.Send(command);

        // Assert
        result.IsSuccess.Should().BeTrue();

        // Note: In a real integration test, you might want to verify that domain event handlers
        // were executed by checking side effects (e.g., welcome emails sent, audit logs created, etc.)
        // For now, we verify the user was created which indicates the domain event was processed
        var user = await DbContext.Users
            .FirstOrDefaultAsync(u => u.Id == new UserId(result.Value));

        user.Should().NotBeNull();
    }

    [Fact]
    public async Task RegisterUser_ShouldHandleConcurrentRegistrations()
    {
        // Arrange
        var email1 = "<EMAIL>";
        var email2 = "<EMAIL>";
        
        var command1 = new RegisterUserCommand(email1, "User", "One", "Password123!");
        var command2 = new RegisterUserCommand(email2, "User", "Two", "Password123!");

        // Act - Execute commands concurrently
        var tasks = new[]
        {
            Sender.Send(command1),
            Sender.Send(command2)
        };

        var results = await Task.WhenAll(tasks);

        // Assert
        results[0].IsSuccess.Should().BeTrue();
        results[1].IsSuccess.Should().BeTrue();
        results[0].Value.Should().NotBe(results[1].Value);

        // Verify both users were created
        var userCount = await DbContext.Users.CountAsync();
        userCount.Should().BeGreaterOrEqualTo(2);
    }

    [Fact]
    public async Task RegisterUser_ShouldPreventDuplicateEmailRegistration()
    {
        // Arrange
        var email = "<EMAIL>";
        var command1 = new RegisterUserCommand(email, "User", "One", "Password123!");
        var command2 = new RegisterUserCommand(email, "User", "Two", "Password123!");

        // Act
        var result1 = await Sender.Send(command1);
        var result2 = await Sender.Send(command2);

        // Assert
        result1.IsSuccess.Should().BeTrue();
        result2.IsFailure.Should().BeTrue();
        result2.Error.Should().Be(UserErrors.EmailNotUnique);

        // Verify only one user was created
        var userCount = await DbContext.Users
            .Where(u => u.Email == new Email(email))
            .CountAsync();
        userCount.Should().Be(1);
    }

    [Fact]
    public async Task RegisterUser_ShouldTrimWhitespaceFromInputs()
    {
        // Arrange
        var command = new RegisterUserCommand(
            "  <EMAIL>  ",
            "  John  ",
            "  Doe  ",
            "SecurePassword123!");

        // Act
        var result = await Sender.Send(command);

        // Assert
        result.IsSuccess.Should().BeTrue();

        var user = await DbContext.Users
            .FirstOrDefaultAsync(u => u.Id == new UserId(result.Value));

        user.Should().NotBeNull();
        user!.FirstName.Should().Be("John");
        user.LastName.Should().Be("Doe");
        user.Email.Value.Should().Be("<EMAIL>");
    }

    [Fact]
    public async Task RegisterUser_ShouldGenerateUniqueUserIds()
    {
        // Arrange
        var commands = Enumerable.Range(1, 5)
            .Select(i => new RegisterUserCommand(
                $"user{i}@test.com",
                $"User{i}",
                "Test",
                "Password123!"))
            .ToArray();

        // Act
        var results = new List<Result<Guid>>();
        foreach (var command in commands)
        {
            results.Add(await Sender.Send(command));
        }

        // Assert
        results.Should().AllSatisfy(r => r.IsSuccess.Should().BeTrue());
        
        var userIds = results.Select(r => r.Value).ToList();
        userIds.Should().OnlyHaveUniqueItems();
    }
}
