using Bookify.Domain.Entities.Apartments;
using Bookify.Domain.Entities.Apartments.Enums;
using Bookify.Domain.Entities.Apartments.ValueObjects;
using Bookify.Domain.Entities.Bookings;
using Bookify.Domain.Entities.Bookings.ValueObjects;
using Bookify.Domain.Entities.Users;
using Bookify.Domain.Entities.Users.ValueObjects;
using Bookify.Domain.Shared;
using Bookify.Infrastructure.Data;

namespace Bookify.Application.IntegrationTests.Infrastructure;

/// <summary>
/// Helper methods for integration tests
/// </summary>
public static class IntegrationTestHelpers
{
    /// <summary>
    /// Creates and saves a test user to the database
    /// </summary>
    public static async Task<User> CreateAndSaveUserAsync(
        ApplicationDbContext dbContext,
        string email = "<EMAIL>",
        string firstName = "Test",
        string lastName = "User")
    {
        var user = User.Create(firstName, lastName, new Email(email));
        dbContext.Users.Add(user);
        await dbContext.SaveChangesAsync();
        return user;
    }

    /// <summary>
    /// Creates and saves a test apartment to the database
    /// </summary>
    public static async Task<Apartment> CreateAndSaveApartmentAsync(
        ApplicationDbContext dbContext,
        string name = "Test Apartment",
        decimal priceAmount = 100m,
        decimal cleaningFeeAmount = 50m,
        List<Amenity>? amenities = null,
        string country = "United States",
        string state = "California",
        string zipCode = "90210",
        string city = "Los Angeles",
        string street = "123 Test Street")
    {
        var apartment = new Apartment(
            ApartmentId.New(),
            name,
            $"Description for {name}",
            new Address(country, state, zipCode, city, street),
            new Money(priceAmount, Currency.Usd),
            new Money(cleaningFeeAmount, Currency.Usd),
            amenities ?? new List<Amenity>());

        dbContext.Apartments.Add(apartment);
        await dbContext.SaveChangesAsync();
        return apartment;
    }

    /// <summary>
    /// Creates and saves a test booking to the database
    /// </summary>
    public static async Task<Booking> CreateAndSaveBookingAsync(
        ApplicationDbContext dbContext,
        Apartment apartment,
        User user,
        DateOnly startDate,
        DateOnly endDate,
        DateTime? createdOnUtc = null)
    {
        var dateRange = DateRange.Create(startDate, endDate);
        var pricingService = new PricingService();
        var booking = Booking.Reserve(
            apartment, 
            user.Id, 
            dateRange, 
            createdOnUtc ?? DateTime.UtcNow, 
            pricingService);

        dbContext.Bookings.Add(booking);
        await dbContext.SaveChangesAsync();
        return booking;
    }

    /// <summary>
    /// Creates a complete booking scenario with user and apartment
    /// </summary>
    public static async Task<(User user, Apartment apartment, Booking booking)> CreateCompleteBookingScenarioAsync(
        ApplicationDbContext dbContext,
        DateOnly startDate,
        DateOnly endDate,
        string userEmail = "<EMAIL>",
        string apartmentName = "Scenario Apartment",
        decimal apartmentPrice = 100m)
    {
        var user = await CreateAndSaveUserAsync(dbContext, userEmail);
        var apartment = await CreateAndSaveApartmentAsync(dbContext, apartmentName, apartmentPrice);
        var booking = await CreateAndSaveBookingAsync(dbContext, apartment, user, startDate, endDate);

        return (user, apartment, booking);
    }

    /// <summary>
    /// Creates multiple apartments with different characteristics for testing
    /// </summary>
    public static async Task<List<Apartment>> CreateVariedApartmentsAsync(ApplicationDbContext dbContext)
    {
        var apartments = new List<Apartment>();

        // Budget apartment
        apartments.Add(await CreateAndSaveApartmentAsync(
            dbContext,
            "Budget Studio",
            50m,
            25m,
            new List<Amenity> { Amenity.WiFi }));

        // Mid-range apartment
        apartments.Add(await CreateAndSaveApartmentAsync(
            dbContext,
            "Cozy Apartment",
            120m,
            60m,
            new List<Amenity> { Amenity.WiFi, Amenity.AirConditioning, Amenity.Parking }));

        // Luxury apartment
        apartments.Add(await CreateAndSaveApartmentAsync(
            dbContext,
            "Luxury Penthouse",
            500m,
            200m,
            new List<Amenity> 
            { 
                Amenity.WiFi, 
                Amenity.AirConditioning, 
                Amenity.Parking,
                Amenity.SwimmingPoool,
                Amenity.Gym,
                Amenity.Spa,
                Amenity.MountainView
            }));

        return apartments;
    }

    /// <summary>
    /// Creates multiple users for testing scenarios
    /// </summary>
    public static async Task<List<User>> CreateMultipleUsersAsync(
        ApplicationDbContext dbContext,
        int count = 3)
    {
        var users = new List<User>();

        for (int i = 1; i <= count; i++)
        {
            var user = await CreateAndSaveUserAsync(
                dbContext,
                $"user{i}@test.com",
                $"User{i}",
                "Test");
            users.Add(user);
        }

        return users;
    }

    /// <summary>
    /// Creates overlapping bookings for testing conflict scenarios
    /// </summary>
    public static async Task<(Booking first, Booking overlapping)> CreateOverlappingBookingsAsync(
        ApplicationDbContext dbContext,
        Apartment apartment,
        DateOnly baseStartDate,
        int overlapDays = 3)
    {
        var users = await CreateMultipleUsersAsync(dbContext, 2);

        var firstBooking = await CreateAndSaveBookingAsync(
            dbContext,
            apartment,
            users[0],
            baseStartDate,
            baseStartDate.AddDays(7));

        var overlappingBooking = await CreateAndSaveBookingAsync(
            dbContext,
            apartment,
            users[1],
            baseStartDate.AddDays(overlapDays),
            baseStartDate.AddDays(10));

        return (firstBooking, overlappingBooking);
    }

    /// <summary>
    /// Creates non-overlapping bookings for testing availability scenarios
    /// </summary>
    public static async Task<(Booking first, Booking second)> CreateNonOverlappingBookingsAsync(
        ApplicationDbContext dbContext,
        Apartment apartment,
        DateOnly baseStartDate,
        int gapDays = 1)
    {
        var users = await CreateMultipleUsersAsync(dbContext, 2);

        var firstBooking = await CreateAndSaveBookingAsync(
            dbContext,
            apartment,
            users[0],
            baseStartDate,
            baseStartDate.AddDays(5));

        var secondBooking = await CreateAndSaveBookingAsync(
            dbContext,
            apartment,
            users[1],
            baseStartDate.AddDays(5 + gapDays),
            baseStartDate.AddDays(10 + gapDays));

        return (firstBooking, secondBooking);
    }

    /// <summary>
    /// Common date ranges for testing
    /// </summary>
    public static class TestDateRanges
    {
        public static readonly DateOnly BaseDate = new(2024, 6, 1);
        
        public static DateOnly NextWeek => BaseDate.AddDays(7);
        public static DateOnly NextMonth => BaseDate.AddDays(30);
        public static DateOnly LastWeek => BaseDate.AddDays(-7);
        public static DateOnly LastMonth => BaseDate.AddDays(-30);

        public static (DateOnly start, DateOnly end) CurrentWeek => (BaseDate, BaseDate.AddDays(6));
        public static (DateOnly start, DateOnly end) NextWeekRange => (NextWeek, NextWeek.AddDays(6));
        public static (DateOnly start, DateOnly end) Weekend => (BaseDate.AddDays(5), BaseDate.AddDays(6)); // Assuming BaseDate is Monday
        public static (DateOnly start, DateOnly end) LongTerm => (BaseDate, BaseDate.AddDays(30));
    }

    /// <summary>
    /// Common test data values
    /// </summary>
    public static class TestData
    {
        public static class Emails
        {
            public const string User1 = "<EMAIL>";
            public const string User2 = "<EMAIL>";
            public const string Guest = "<EMAIL>";
            public const string Host = "<EMAIL>";
            public const string Admin = "<EMAIL>";
        }

        public static class ApartmentNames
        {
            public const string Budget = "Budget Studio";
            public const string Standard = "Standard Apartment";
            public const string Luxury = "Luxury Penthouse";
            public const string Beachfront = "Beachfront Villa";
            public const string Mountain = "Mountain Cabin";
        }

        public static class Prices
        {
            public const decimal Budget = 50m;
            public const decimal Standard = 120m;
            public const decimal Luxury = 500m;
            public const decimal CleaningFeeStandard = 50m;
            public const decimal CleaningFeeLuxury = 200m;
        }
    }

    /// <summary>
    /// Cleans up test data (use with caution in integration tests)
    /// </summary>
    public static async Task CleanupTestDataAsync(ApplicationDbContext dbContext)
    {
        // Note: This should be used carefully and typically only in isolated test environments
        // In most cases, using transactions or separate test databases is preferred
        
        dbContext.Bookings.RemoveRange(dbContext.Bookings);
        dbContext.Users.RemoveRange(dbContext.Users);
        dbContext.Apartments.RemoveRange(dbContext.Apartments);
        
        await dbContext.SaveChangesAsync();
    }
}
