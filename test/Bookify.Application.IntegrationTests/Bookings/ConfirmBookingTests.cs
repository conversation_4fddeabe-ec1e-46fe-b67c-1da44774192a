using Bookify.Application.Bookings.ConfirmBooking;
using Bookify.Application.Bookings.ReserveBooking;
using Bookify.Application.IntegrationTests.Infrastructure;
using Bookify.Domain.Entities.Apartments;
using Bookify.Domain.Entities.Apartments.ValueObjects;
using Bookify.Domain.Entities.Bookings;
using Bookify.Domain.Entities.Bookings.ValueObjects;
using Bookify.Domain.Entities.Users;
using Bookify.Domain.Entities.Users.ValueObjects;
using Bookify.Domain.Shared;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;

namespace Bookify.Application.IntegrationTests.Bookings;

public class ConfirmBookingTests : BaseIntegrationTest
{
    private static readonly DateOnly StartDate = new DateOnly(2024, 6, 1);
    private static readonly DateOnly EndDate = new DateOnly(2024, 6, 10);

    public ConfirmBookingTests(IntegrationTestWebAppFactory webAppFactory) : base(webAppFactory)
    {
    }

    [Fact]
    public async Task ConfirmBooking_ShouldReturnFailure_WhenBookingNotFound()
    {
        // Arrange
        var nonExistentBookingId = Guid.NewGuid();
        var command = new ConfirmBookingCommand(nonExistentBookingId);

        // Act
        var result = await Sender.Send(command);

        // Assert
        result.Error.Should().Be(BookingErrors.NotFound);
    }

    [Fact]
    public async Task ConfirmBooking_ShouldReturnSuccess_WhenBookingIsReserved()
    {
        // Arrange
        var booking = await CreateReservedBookingAsync();
        var command = new ConfirmBookingCommand(booking.Id.Value);

        // Act
        var result = await Sender.Send(command);

        // Assert
        result.IsSuccess.Should().BeTrue();

        // Verify booking status was updated
        var updatedBooking = await DbContext.Bookings
            .FirstOrDefaultAsync(b => b.Id == booking.Id);

        updatedBooking.Should().NotBeNull();
        updatedBooking!.Status.Should().Be(BookingStatus.Confirmed);
        updatedBooking.ConfirmedOnUtc.Should().NotBeNull();
    }

    [Fact]
    public async Task ConfirmBooking_ShouldReturnFailure_WhenBookingIsNotReserved()
    {
        // Arrange
        var booking = await CreateReservedBookingAsync();
        
        // First confirm the booking
        var firstConfirmCommand = new ConfirmBookingCommand(booking.Id.Value);
        await Sender.Send(firstConfirmCommand);

        // Try to confirm again
        var secondConfirmCommand = new ConfirmBookingCommand(booking.Id.Value);

        // Act
        var result = await Sender.Send(secondConfirmCommand);

        // Assert
        result.Error.Should().Be(BookingErrors.NotReserved);
    }

    [Fact]
    public async Task ConfirmBooking_ShouldRaiseDomainEvent()
    {
        // Arrange
        var booking = await CreateReservedBookingAsync();
        var command = new ConfirmBookingCommand(booking.Id.Value);

        // Act
        var result = await Sender.Send(command);

        // Assert
        result.IsSuccess.Should().BeTrue();

        // Note: In a real integration test, you might want to verify that domain event handlers
        // were executed by checking side effects (e.g., emails sent, notifications created, etc.)
        // For now, we verify the booking state change which indicates the domain event was processed
        var updatedBooking = await DbContext.Bookings
            .FirstOrDefaultAsync(b => b.Id == booking.Id);

        updatedBooking!.Status.Should().Be(BookingStatus.Confirmed);
    }

    [Fact]
    public async Task ConfirmBooking_ShouldUpdateBookingInDatabase()
    {
        // Arrange
        var booking = await CreateReservedBookingAsync();
        var beforeConfirmation = DateTime.UtcNow;
        var command = new ConfirmBookingCommand(booking.Id.Value);

        // Act
        var result = await Sender.Send(command);

        // Assert
        result.IsSuccess.Should().BeTrue();

        var updatedBooking = await DbContext.Bookings
            .FirstOrDefaultAsync(b => b.Id == booking.Id);

        updatedBooking.Should().NotBeNull();
        updatedBooking!.Status.Should().Be(BookingStatus.Confirmed);
        updatedBooking.ConfirmedOnUtc.Should().NotBeNull();
        updatedBooking.ConfirmedOnUtc.Should().BeAfter(beforeConfirmation);
        
        // Other properties should remain unchanged
        updatedBooking.UserId.Should().Be(booking.UserId);
        updatedBooking.ApartmentId.Should().Be(booking.ApartmentId);
        updatedBooking.Duration.Should().Be(booking.Duration);
        updatedBooking.TotalPrice.Should().Be(booking.TotalPrice);
    }

    [Theory]
    [InlineData(BookingStatus.Rejected)]
    [InlineData(BookingStatus.Completed)]
    [InlineData(BookingStatus.Cancelled)]
    public async Task ConfirmBooking_ShouldReturnFailure_WhenBookingIsInInvalidStatus(BookingStatus invalidStatus)
    {
        // Arrange
        var booking = await CreateReservedBookingAsync();
        
        // Change booking to invalid status using direct database manipulation
        // (In a real scenario, you'd use the appropriate commands)
        await UpdateBookingStatusDirectlyAsync(booking.Id, invalidStatus);

        var command = new ConfirmBookingCommand(booking.Id.Value);

        // Act
        var result = await Sender.Send(command);

        // Assert
        result.Error.Should().Be(BookingErrors.NotReserved);
    }

    private async Task<Booking> CreateReservedBookingAsync()
    {
        var user = await CreateAndSaveUserAsync();
        var apartment = await CreateAndSaveApartmentAsync();
        
        var reserveCommand = new ReserveBookingCommand(apartment.Id.Value, user.Id.Value, StartDate, EndDate);
        var reserveResult = await Sender.Send(reserveCommand);
        
        reserveResult.IsSuccess.Should().BeTrue();

        var booking = await DbContext.Bookings
            .FirstOrDefaultAsync(b => b.Id == new BookingId(reserveResult.Value));

        booking.Should().NotBeNull();
        return booking!;
    }

    private async Task<User> CreateAndSaveUserAsync(string email = "<EMAIL>")
    {
        var user = User.Create("Test", "User", new Email(email));
        DbContext.Users.Add(user);
        await DbContext.SaveChangesAsync();
        return user;
    }

    private async Task<Apartment> CreateAndSaveApartmentAsync()
    {
        var apartment = new Apartment(
            ApartmentId.New(),
            "Test Apartment",
            "Test Description",
            new Address("Country", "State", "12345", "City", "Street"),
            new Money(100m, Currency.Usd),
            new Money(50m, Currency.Usd),
            new List<Domain.Entities.Apartments.Enums.Amenity>());

        DbContext.Apartments.Add(apartment);
        await DbContext.SaveChangesAsync();
        return apartment;
    }

    private async Task UpdateBookingStatusDirectlyAsync(BookingId bookingId, BookingStatus status)
    {
        var booking = await DbContext.Bookings.FirstOrDefaultAsync(b => b.Id == bookingId);
        if (booking != null)
        {
            // Use reflection to set the status directly for testing purposes
            var statusProperty = typeof(Booking).GetProperty(nameof(Booking.Status));
            statusProperty?.SetValue(booking, status);
            
            await DbContext.SaveChangesAsync();
        }
    }
}
