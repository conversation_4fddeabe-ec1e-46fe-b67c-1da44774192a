﻿using Bookify.Application.Bookings.ReserveBooking;
using Bookify.Application.IntegrationTests.Infrastructure;
using Bookify.Domain.Apartments;
using Bookify.Domain.Entities.Apartments;
using Bookify.Domain.Entities.Apartments.ValueObjects;
using Bookify.Domain.Entities.Bookings;
using Bookify.Domain.Entities.Bookings.ValueObjects;
using Bookify.Domain.Entities.Users;
using Bookify.Domain.Entities.Users.ValueObjects;
using Bookify.Domain.Shared;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;

namespace Bookify.Application.IntegrationTests.Bookings;
public class ReserveBookingTests : BaseIntegrationTest
{
    private static readonly DateOnly StartDate = new DateOnly(2024, 6, 1);
    private static readonly DateOnly EndDate = new DateOnly(2024, 6, 10);

    public ReserveBookingTests(IntegrationTestWebAppFactory webAppFactory) : base(webAppFactory)
    {
    }

    [Fact]
    public async Task ReserveBooking_ShouldReturnFailure_WhenUserIsNotFound()
    {
        // Arrange
        var apartmentId = Guid.NewGuid();
        var userId = Guid.NewGuid();
        var command = new ReserveBookingCommand(apartmentId, userId, StartDate, EndDate);

        // Act
        var result = await Sender.Send(command);

        // Assert
        result.Error.Should().Be(UserErrors.NotFound);
    }

    [Fact]
    public async Task ReserveBooking_ShouldReturnFailure_WhenApartmentIsNotFound()
    {
        // Arrange
        var user = await CreateAndSaveUserAsync();
        var apartmentId = Guid.NewGuid();
        var command = new ReserveBookingCommand(apartmentId, user.Id.Value, StartDate, EndDate);

        // Act
        var result = await Sender.Send(command);

        // Assert
        result.Error.Should().Be(ApartmentErrors.NotFound);
    }

    [Fact]
    public async Task ReserveBooking_ShouldReturnSuccess_WhenValidDataProvided()
    {
        // Arrange
        var user = await CreateAndSaveUserAsync();
        var apartment = await CreateAndSaveApartmentAsync();
        var command = new ReserveBookingCommand(apartment.Id.Value, user.Id.Value, StartDate, EndDate);

        // Act
        var result = await Sender.Send(command);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().NotBe(Guid.Empty);

        // Verify booking was saved to database
        var booking = await DbContext.Bookings
            .FirstOrDefaultAsync(b => b.Id == new BookingId(result.Value));

        booking.Should().NotBeNull();
        booking!.UserId.Should().Be(user.Id);
        booking.ApartmentId.Should().Be(apartment.Id);
        booking.Status.Should().Be(BookingStatus.Reserved);
    }

    [Fact]
    public async Task ReserveBooking_ShouldReturnFailure_WhenBookingOverlaps()
    {
        // Arrange
        var user1 = await CreateAndSaveUserAsync("<EMAIL>");
        var user2 = await CreateAndSaveUserAsync("<EMAIL>");
        var apartment = await CreateAndSaveApartmentAsync();

        // Create first booking
        var firstCommand = new ReserveBookingCommand(apartment.Id.Value, user1.Id.Value, StartDate, EndDate);
        var firstResult = await Sender.Send(firstCommand);
        firstResult.IsSuccess.Should().BeTrue();

        // Try to create overlapping booking
        var overlappingStartDate = StartDate.AddDays(2);
        var overlappingEndDate = EndDate.AddDays(2);
        var overlappingCommand = new ReserveBookingCommand(apartment.Id.Value, user2.Id.Value, overlappingStartDate, overlappingEndDate);

        // Act
        var result = await Sender.Send(overlappingCommand);

        // Assert
        result.Error.Should().Be(BookingErrors.Overlap);
    }

    [Fact]
    public async Task ReserveBooking_ShouldCalculateCorrectPricing()
    {
        // Arrange
        var user = await CreateAndSaveUserAsync();
        var apartment = await CreateAndSaveApartmentAsync(priceAmount: 100m, cleaningFeeAmount: 50m);
        var command = new ReserveBookingCommand(apartment.Id.Value, user.Id.Value, StartDate, EndDate);

        // Act
        var result = await Sender.Send(command);

        // Assert
        result.IsSuccess.Should().BeTrue();

        var booking = await DbContext.Bookings
            .FirstOrDefaultAsync(b => b.Id == new BookingId(result.Value));

        booking.Should().NotBeNull();

        var expectedDays = (EndDate.ToDateTime(TimeOnly.MinValue) - StartDate.ToDateTime(TimeOnly.MinValue)).Days + 1;
        var expectedPriceForPeriod = 100m * expectedDays;
        var expectedTotalPrice = expectedPriceForPeriod + 50m; // Including cleaning fee

        booking!.PriceForPeriod.Amount.Should().Be(expectedPriceForPeriod);
        booking.CleaningFee.Amount.Should().Be(50m);
        booking.TotalPrice.Amount.Should().Be(expectedTotalPrice);
    }

    [Fact]
    public async Task ReserveBooking_ShouldAllowNonOverlappingBookings()
    {
        // Arrange
        var user1 = await CreateAndSaveUserAsync("<EMAIL>");
        var user2 = await CreateAndSaveUserAsync("<EMAIL>");
        var apartment = await CreateAndSaveApartmentAsync();

        // Create first booking
        var firstCommand = new ReserveBookingCommand(apartment.Id.Value, user1.Id.Value, StartDate, EndDate);
        var firstResult = await Sender.Send(firstCommand);
        firstResult.IsSuccess.Should().BeTrue();

        // Create non-overlapping booking (after the first one)
        var nonOverlappingStartDate = EndDate.AddDays(1);
        var nonOverlappingEndDate = nonOverlappingStartDate.AddDays(5);
        var nonOverlappingCommand = new ReserveBookingCommand(apartment.Id.Value, user2.Id.Value, nonOverlappingStartDate, nonOverlappingEndDate);

        // Act
        var result = await Sender.Send(nonOverlappingCommand);

        // Assert
        result.IsSuccess.Should().BeTrue();

        // Verify both bookings exist
        var bookingCount = await DbContext.Bookings
            .Where(b => b.ApartmentId == apartment.Id)
            .CountAsync();

        bookingCount.Should().Be(2);
    }

    private async Task<User> CreateAndSaveUserAsync(string email = "<EMAIL>")
    {
        var user = User.Create("Test", "User", new Email(email));
        DbContext.Users.Add(user);
        await DbContext.SaveChangesAsync();
        return user;
    }

    private async Task<Apartment> CreateAndSaveApartmentAsync(decimal priceAmount = 100m, decimal cleaningFeeAmount = 50m)
    {
        var apartment = new Apartment(
            ApartmentId.New(),
            "Test Apartment",
            "Test Description",
            new Address("Country", "State", "12345", "City", "Street"),
            new Money(priceAmount, Currency.Usd),
            new Money(cleaningFeeAmount, Currency.Usd),
            new List<Domain.Entities.Apartments.Enums.Amenity>());

        DbContext.Apartments.Add(apartment);
        await DbContext.SaveChangesAsync();
        return apartment;
    }
}
