﻿using Bookify.Application.Apartments.SearchApartments;
using Bookify.Application.IntegrationTests.Infrastructure;
using Bookify.Domain.Entities.Apartments;
using Bookify.Domain.Entities.Apartments.Enums;
using Bookify.Domain.Entities.Apartments.ValueObjects;
using Bookify.Domain.Entities.Bookings.ValueObjects;
using Bookify.Domain.Shared;
using FluentAssertions;

namespace Bookify.Application.IntegrationTests.Apartments;
public class SearchApartmentsTests : BaseIntegrationTest
{
    public SearchApartmentsTests(IntegrationTestWebAppFactory webAppFactory)
        : base(webAppFactory)
    {
    }

    [Fact]
    public async Task SearchApartments_ShouldReturnEmptyList_WhenDateRangeIsInvalid()
    {
        // Arrange
        var invalidStartDate = new DateOnly(2024, 6, 10);
        var invalidEndDate = new DateOnly(2024, 6, 1); // End before start
        var query = new SearchApartmentsQuery(invalidStartDate, invalidEndDate);

        // Act
        var result = await Sender.Send(query);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeEmpty();
    }

    [Fact]
    public async Task SearchApartments_ShouldReturnAvailableApartments_WhenValidDateRange()
    {
        // Arrange
        var apartment1 = await CreateAndSaveApartmentAsync("Available Apartment 1", 100m);
        var apartment2 = await CreateAndSaveApartmentAsync("Available Apartment 2", 150m);

        var startDate = new DateOnly(2024, 6, 1);
        var endDate = new DateOnly(2024, 6, 10);
        var query = new SearchApartmentsQuery(startDate, endDate);

        // Act
        var result = await Sender.Send(query);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().HaveCountGreaterOrEqualTo(2);

        var apartmentIds = result.Value.Select(a => a.Id).ToList();
        apartmentIds.Should().Contain(apartment1.Id.Value);
        apartmentIds.Should().Contain(apartment2.Id.Value);
    }

    [Fact]
    public async Task SearchApartments_ShouldExcludeBookedApartments()
    {
        // Arrange
        var availableApartment = await CreateAndSaveApartmentAsync("Available Apartment", 100m);
        var bookedApartment = await CreateAndSaveApartmentAsync("Booked Apartment", 120m);

        // Create a booking that overlaps with search dates
        var searchStartDate = new DateOnly(2024, 6, 1);
        var searchEndDate = new DateOnly(2024, 6, 10);

        await CreateBookingAsync(bookedApartment, searchStartDate, searchEndDate);

        var query = new SearchApartmentsQuery(searchStartDate, searchEndDate);

        // Act
        var result = await Sender.Send(query);

        // Assert
        result.IsSuccess.Should().BeTrue();

        var apartmentIds = result.Value.Select(a => a.Id).ToList();
        apartmentIds.Should().Contain(availableApartment.Id.Value);
        apartmentIds.Should().NotContain(bookedApartment.Id.Value);
    }

    [Fact]
    public async Task SearchApartments_ShouldIncludeApartmentDetails()
    {
        // Arrange
        var apartment = await CreateAndSaveApartmentAsync(
            name: "Luxury Penthouse",
            priceAmount: 300m,
            cleaningFeeAmount: 100m,
            amenities: new List<Amenity> { Amenity.WiFi, Amenity.SwimmingPoool, Amenity.MountainView });

        var startDate = new DateOnly(2024, 6, 1);
        var endDate = new DateOnly(2024, 6, 10);
        var query = new SearchApartmentsQuery(startDate, endDate);

        // Act
        var result = await Sender.Send(query);

        // Assert
        result.IsSuccess.Should().BeTrue();

        var foundApartment = result.Value.FirstOrDefault(a => a.Id == apartment.Id.Value);
        foundApartment.Should().NotBeNull();
        foundApartment!.Name.Should().Be("Luxury Penthouse");
        foundApartment.Price.Amount.Should().Be(300m);
        foundApartment.Price.Currency.Should().Be(Currency.Usd);
        foundApartment.CleaningFee.Amount.Should().Be(100m);
        foundApartment.Amenities.Should().Contain(Amenity.WiFi);
        foundApartment.Amenities.Should().Contain(Amenity.SwimmingPoool);
        foundApartment.Amenities.Should().Contain(Amenity.MountainView);
    }

    [Fact]
    public async Task SearchApartments_ShouldReturnApartmentsOrderedByPrice()
    {
        // Arrange
        var expensiveApartment = await CreateAndSaveApartmentAsync("Expensive", 500m);
        var cheapApartment = await CreateAndSaveApartmentAsync("Cheap", 50m);
        var midRangeApartment = await CreateAndSaveApartmentAsync("Mid-range", 150m);

        var startDate = new DateOnly(2024, 6, 1);
        var endDate = new DateOnly(2024, 6, 10);
        var query = new SearchApartmentsQuery(startDate, endDate);

        // Act
        var result = await Sender.Send(query);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().HaveCountGreaterOrEqualTo(3);

        // Verify apartments are ordered by price (assuming ascending order)
        var prices = result.Value.Select(a => a.Price.Amount).ToList();
        prices.Should().BeInAscendingOrder();
    }

    [Fact]
    public async Task SearchApartments_ShouldHandlePartiallyOverlappingBookings()
    {
        // Arrange
        var apartment = await CreateAndSaveApartmentAsync("Test Apartment", 100m);

        // Create a booking that partially overlaps with search dates
        var existingBookingStart = new DateOnly(2024, 5, 25);
        var existingBookingEnd = new DateOnly(2024, 6, 5);
        await CreateBookingAsync(apartment, existingBookingStart, existingBookingEnd);

        // Search for dates that overlap with existing booking
        var searchStartDate = new DateOnly(2024, 6, 1);
        var searchEndDate = new DateOnly(2024, 6, 10);
        var query = new SearchApartmentsQuery(searchStartDate, searchEndDate);

        // Act
        var result = await Sender.Send(query);

        // Assert
        result.IsSuccess.Should().BeTrue();

        var apartmentIds = result.Value.Select(a => a.Id).ToList();
        apartmentIds.Should().NotContain(apartment.Id.Value);
    }

    [Fact]
    public async Task SearchApartments_ShouldIncludeApartmentsWithNonOverlappingBookings()
    {
        // Arrange
        var apartment = await CreateAndSaveApartmentAsync("Test Apartment", 100m);

        // Create a booking that doesn't overlap with search dates
        var nonOverlappingBookingStart = new DateOnly(2024, 5, 1);
        var nonOverlappingBookingEnd = new DateOnly(2024, 5, 10);
        await CreateBookingAsync(apartment, nonOverlappingBookingStart, nonOverlappingBookingEnd);

        // Search for dates that don't overlap with existing booking
        var searchStartDate = new DateOnly(2024, 6, 1);
        var searchEndDate = new DateOnly(2024, 6, 10);
        var query = new SearchApartmentsQuery(searchStartDate, searchEndDate);

        // Act
        var result = await Sender.Send(query);

        // Assert
        result.IsSuccess.Should().BeTrue();

        var apartmentIds = result.Value.Select(a => a.Id).ToList();
        apartmentIds.Should().Contain(apartment.Id.Value);
    }

    private async Task<Apartment> CreateAndSaveApartmentAsync(
        string name,
        decimal priceAmount,
        decimal cleaningFeeAmount = 50m,
        List<Amenity>? amenities = null)
    {
        var apartment = new Apartment(
            ApartmentId.New(),
            name,
            $"Description for {name}",
            new Address("United States", "California", "90210", "Los Angeles", "123 Test Street"),
            new Money(priceAmount, Currency.Usd),
            new Money(cleaningFeeAmount, Currency.Usd),
            amenities ?? new List<Amenity>());

        DbContext.Apartments.Add(apartment);
        await DbContext.SaveChangesAsync();
        return apartment;
    }

    private async Task CreateBookingAsync(Apartment apartment, DateOnly startDate, DateOnly endDate)
    {
        // Create a user for the booking
        var user = Domain.Entities.Users.User.Create("Test", "User", new Domain.Entities.Users.ValueObjects.Email("<EMAIL>"));
        DbContext.Users.Add(user);
        await DbContext.SaveChangesAsync();

        // Create the booking using the domain method
        var dateRange = DateRange.Create(startDate, endDate);
        var pricingService = new Domain.Entities.Bookings.PricingService();
        var booking = Domain.Entities.Bookings.Booking.Reserve(apartment, user.Id, dateRange, DateTime.UtcNow, pricingService);

        DbContext.Bookings.Add(booking);
        await DbContext.SaveChangesAsync();
    }
}
