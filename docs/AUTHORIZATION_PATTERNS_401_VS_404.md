# Authorization Patterns: 401 vs 404 Responses

## 🎯 **The Problem You Want to Solve**

When User1 tries to update/modify a record belonging to <PERSON><PERSON><PERSON>, you want to return **401 Unauthorized** instead of **404 Not Found** to clearly indicate it's an authorization issue.

## 🔍 **When to Return 401 vs 404**

### **404 Not Found** - Use for READ operations
- User tries to view a resource that doesn't exist OR doesn't belong to them
- Prevents information disclosure (user can't tell if resource exists)
- Example: `GET /api/bookings/123` where booking doesn't exist or belongs to another user

### **401 Unauthorized** - Use for WRITE operations  
- User tries to modify/delete a resource that belongs to another user
- Resource exists, but user doesn't have permission to modify it
- Example: `PUT /api/bookings/123` where booking exists but belongs to another user

## ✅ **Complete Solution Implementation**

### **1. Enhanced UserAwareRepository**

```csharp
public abstract class UserAwareRepository<TEntity, TEntityId> : Repository<TEntity, TEntityId>
{
    // For READ operations - returns null if not found OR not owned (security through obscurity)
    public async Task<TEntity?> GetByIdForCurrentUserAsync(TEntityId id, ...)
    {
        return await DbContext.Set<TEntity>()
            .Where(entity => entity.Id == id)
            .Where(GetUserFilter())  // Only returns if owned by user
            .FirstOrDefaultAsync();
    }

    // For WRITE operations - distinguishes between NotFound and Unauthorized
    public async Task<(TEntity? Entity, AuthorizationResult Result)> GetForUpdateAsync(TEntityId id, ...)
    {
        // First check if entity exists at all
        var entity = await DbContext.Set<TEntity>()
            .FirstOrDefaultAsync(e => e.Id == id);

        if (entity is null)
            return (null, AuthorizationResult.NotFound);  // 404 - Resource doesn't exist

        // Check if user owns it
        if (!GetUserFilter().Compile()(entity))
            return (null, AuthorizationResult.Unauthorized);  // 401 - Exists but not owned

        return (entity, AuthorizationResult.Success);
    }
}
```

### **2. Authorization Result Handling**

```csharp
public enum AuthorizationResult
{
    Success,      // User owns the resource
    NotFound,     // Resource doesn't exist -> 404
    Unauthorized  // Resource exists but user doesn't own it -> 401
}

public static class AuthorizationResultExtensions
{
    public static Error ToError(this AuthorizationResult result)
    {
        return result switch
        {
            AuthorizationResult.NotFound => AuthorizationErrors.ResourceNotFound,      // 404
            AuthorizationResult.Unauthorized => AuthorizationErrors.Unauthorized,     // 401
            _ => throw new ArgumentOutOfRangeException(nameof(result))
        };
    }
}
```

### **3. Command Handler Examples**

#### **READ Operation (GET) - Returns 404 for both cases**
```csharp
public class GetBookingQueryHandler : IQueryHandler<GetBookingQuery, BookingResponse>
{
    public async Task<Result<BookingResponse>> Handle(...)
    {
        // Use method that returns null for both "not found" and "not owned"
        var booking = await _bookingRepository.GetByIdForCurrentUserAsync(id);
        
        if (booking is null)
            return Result.Failure<BookingResponse>(BookingErrors.NotFound);  // Always 404
            
        return MapToResponse(booking);
    }
}
```

#### **WRITE Operation (PUT/DELETE) - Returns 404 or 401 appropriately**
```csharp
public class UpdateBookingCommandHandler : ICommandHandler<UpdateBookingCommand>
{
    public async Task<Result> Handle(...)
    {
        // Use method that distinguishes between not found and unauthorized
        var (booking, authResult) = await _bookingRepository.GetForUpdateAsync(id);

        if (authResult != AuthorizationResult.Success)
        {
            return Result.Failure(authResult.ToError());
            // Returns 404 if booking doesn't exist
            // Returns 401 if booking exists but belongs to another user
        }

        // Perform update logic
        booking.Update(...);
        await _unitOfWork.SaveChangesAsync();
        
        return Result.Success();
    }
}
```

### **4. Controller Implementation**

```csharp
[HttpGet("{id}")]
public async Task<IActionResult> GetBooking(Guid id)
{
    var result = await _sender.Send(new GetBookingQuery(id));
    
    if (result.IsFailure)
        return NotFound();  // Always 404 for reads (security through obscurity)
        
    return Ok(result.Value);
}

[HttpPut("{id}")]
public async Task<IActionResult> UpdateBooking(Guid id, UpdateBookingRequest request)
{
    var result = await _sender.Send(new UpdateBookingCommand(id, ...));
    
    if (result.IsFailure)
    {
        return result.Error.Code switch
        {
            "Authorization.ResourceNotFound" => NotFound(),      // 404 - Booking doesn't exist
            "Authorization.Unauthorized" => Unauthorized(),     // 401 - Booking exists but not owned
            _ => BadRequest(result.Error)
        };
    }
    
    return Ok();
}

[HttpDelete("{id}")]
public async Task<IActionResult> DeleteBooking(Guid id)
{
    var result = await _sender.Send(new DeleteBookingCommand(id));
    
    if (result.IsFailure)
    {
        return result.Error.Code switch
        {
            "Authorization.ResourceNotFound" => NotFound(),      // 404 - Booking doesn't exist  
            "Authorization.Unauthorized" => Unauthorized(),     // 401 - Booking exists but not owned
            _ => BadRequest(result.Error)
        };
    }
    
    return NoContent();
}
```

## 🔒 **Security Benefits**

### **For READ Operations (404 for both cases):**
- **Information Disclosure Prevention**: Attackers can't determine if a resource exists
- **Consistent Behavior**: Same response whether resource doesn't exist or isn't owned

### **For WRITE Operations (401 vs 404):**
- **Clear Error Messages**: Developers know exactly what went wrong
- **Proper HTTP Semantics**: 401 means "you're not allowed", 404 means "doesn't exist"
- **Better User Experience**: Frontend can show appropriate error messages

## 📊 **Response Matrix**

| Operation | Resource Exists | User Owns | Response | Reason |
|-----------|----------------|-----------|----------|---------|
| **GET** | ❌ No | N/A | 404 | Resource doesn't exist |
| **GET** | ✅ Yes | ❌ No | 404 | Hide existence from unauthorized users |
| **GET** | ✅ Yes | ✅ Yes | 200 | Success |
| **PUT/DELETE** | ❌ No | N/A | 404 | Resource doesn't exist |
| **PUT/DELETE** | ✅ Yes | ❌ No | **401** | Resource exists but unauthorized |
| **PUT/DELETE** | ✅ Yes | ✅ Yes | 200/204 | Success |

## 🎯 **Key Takeaway**

- **READ operations**: Always return 404 (security through obscurity)
- **WRITE operations**: Return 404 if doesn't exist, 401 if exists but not owned

This approach gives you the exact behavior you want: **401 Unauthorized when User1 tries to modify User2's data!**
