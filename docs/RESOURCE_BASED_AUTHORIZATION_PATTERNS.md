# Resource-Based Authorization Patterns

## 🎯 **Problem Statement**

You're absolutely right! The current approach of manually checking `booking.UserId != _userContext.UserId` in every query handler is:

- ❌ **Error-prone**: Easy to forget the check
- ❌ **Repetitive**: Same logic scattered everywhere  
- ❌ **Hard to maintain**: Changes require updating multiple files
- ❌ **Inconsistent**: Different handlers might implement it differently

## ✅ **Better Approaches**

### **Option 1: User-Aware Repository Pattern (Recommended)**

#### **Benefits:**
- ✅ Centralized authorization logic
- ✅ Impossible to forget user filtering
- ✅ Clean separation of concerns
- ✅ Follows Clean Architecture principles

#### **Implementation:**

```csharp
// Base class for user-aware repositories
public abstract class UserAwareRepository<TEntity, TEntityId> : Repository<TEntity, TEntityId>
{
    protected readonly IUserContext _userContext;
    
    protected abstract Expression<Func<TEntity, bool>> GetUserFilter();
    
    public async Task<TEntity?> GetByIdForCurrentUserAsync(TEntityId id, ...)
    {
        return await DbContext.Set<TEntity>()
            .Where(entity => entity.Id == id)
            .Where(GetUserFilter())  // 🔒 Automatic user filtering
            .FirstOrDefaultAsync();
    }
}

// Specific implementation
public class BookingRepository : UserAwareRepository<Booking, BookingId>
{
    protected override Expression<Func<Booking, bool>> GetUserFilter()
    {
        return booking => booking.UserId == _userContext.UserId;
    }
}

// Usage in query handler - NO manual authorization check needed!
public class GetBookingQueryHandler : IQueryHandler<GetBookingQuery, BookingResponse>
{
    public async Task<Result<BookingResponse>> Handle(...)
    {
        var booking = await _bookingRepository.GetByIdForCurrentUserAsync(id);
        if (booking is null) return NotFound; // Already filtered by user!
        return MapToResponse(booking);
    }
}
```

### **Option 2: SQL-Based with User Context**

#### **Benefits:**
- ✅ High performance (database-level filtering)
- ✅ Automatic user filtering in SQL
- ✅ No risk of forgetting authorization

#### **Implementation:**

```csharp
public class GetBookingQueryHandler : IQueryHandler<GetBookingQuery, BookingResponse>
{
    public async Task<Result<BookingResponse>> Handle(...)
    {
        const string sql = """
            SELECT * FROM bookings 
            WHERE id = @BookingId 
              AND user_id = @CurrentUserId  -- 🔒 Always filter by user
            """;
            
        var booking = await connection.QueryFirstOrDefaultAsync<BookingResponse>(
            sql, 
            new { 
                request.BookingId, 
                CurrentUserId = _userContext.UserId 
            });
            
        // No manual check needed - SQL handles authorization!
        return booking ?? NotFound;
    }
}
```

### **Option 3: Authorization Middleware/Filters**

#### **Benefits:**
- ✅ Declarative authorization
- ✅ Applied via attributes
- ✅ Centralized policy management

#### **Implementation:**

```csharp
// Custom authorization attribute
[ResourceOwnership("Booking")]
[HttpGet("{id}")]
public async Task<IActionResult> GetBooking(Guid id)
{
    // Authorization handled by middleware
    var booking = await _bookingRepository.GetByIdAsync(id);
    return Ok(booking);
}

// Authorization handler
public class ResourceOwnershipHandler : AuthorizationHandler<ResourceOwnershipRequirement>
{
    protected override async Task HandleRequirementAsync(...)
    {
        // Extract resource ID from route
        // Check ownership in database
        // Succeed or fail authorization
    }
}
```

### **Option 4: Domain Service Pattern**

#### **Benefits:**
- ✅ Business logic encapsulation
- ✅ Reusable across different contexts
- ✅ Clear domain boundaries

#### **Implementation:**

```csharp
public class BookingAccessService
{
    public async Task<Result<Booking>> GetUserBookingAsync(BookingId id, UserId userId)
    {
        var booking = await _repository.GetByIdAsync(id);
        
        if (booking is null || booking.UserId != userId)
            return Result.Failure<Booking>(BookingErrors.NotFound);
            
        return booking;
    }
}

// Usage
public class GetBookingQueryHandler
{
    public async Task<Result<BookingResponse>> Handle(...)
    {
        var result = await _bookingAccessService.GetUserBookingAsync(
            new BookingId(request.BookingId), 
            _userContext.UserId);
            
        return result.IsSuccess ? MapToResponse(result.Value) : result.Error;
    }
}
```

## 🏆 **Recommended Approach: User-Aware Repository**

For your Bookify application, I recommend **Option 1: User-Aware Repository Pattern** because:

### **Why This Approach?**

1. **Clean Architecture Compliance**: Keeps authorization logic in infrastructure layer
2. **Type Safety**: Compile-time guarantees that user filtering is applied
3. **Performance**: Database-level filtering (no unnecessary data retrieval)
4. **Maintainability**: Single place to change user filtering logic
5. **Testability**: Easy to mock and test

### **Implementation Steps:**

1. **Create base `UserAwareRepository<T, TId>` class**
2. **Update specific repositories** (BookingRepository, ReviewRepository, etc.)
3. **Update repository interfaces** to include user-aware methods
4. **Update query handlers** to use user-aware methods
5. **Remove manual authorization checks** from handlers

### **Migration Strategy:**

```csharp
// Phase 1: Add user-aware methods alongside existing ones
public interface IBookingRepository
{
    Task<Booking> GetByIdAsync(BookingId id);                    // Keep existing
    Task<Booking?> GetByIdForCurrentUserAsync(BookingId id);     // Add new
}

// Phase 2: Update handlers to use new methods
// Phase 3: Remove old methods and manual checks
```

## 🔒 **Security Benefits**

- **Impossible to forget**: User filtering is built into the repository
- **Consistent**: Same pattern across all user-owned resources
- **Auditable**: Clear separation between admin and user operations
- **Performance**: Database-level filtering reduces data transfer

## 📝 **Code Examples**

See the implemented files:
- `UserAwareRepository.cs` - Base repository class
- `BookingRepository.cs` - Updated with user-aware methods
- `GetBookingQueryHandler.cs` - Clean implementation without manual checks

This approach eliminates the need for manual authorization checks while maintaining security and improving code quality!
