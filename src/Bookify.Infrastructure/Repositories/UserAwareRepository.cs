using Bookify.Application.Abstractions.Authentication;
using Bookify.Domain.Entities.Abstractions;
using Bookify.Infrastructure.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace Bookify.Infrastructure.Repositories;

public abstract class UserAwareRepository<TEntity, TEntityId> : Repository<TEntity, TEntityId>
    where TEntity : Entity<TEntityId>
    where TEntityId : class
{
    protected readonly IUserContext _userContext;

    protected UserAwareRepository(ApplicationDbContext dbContext, IUserContext userContext)
        : base(dbContext)
    {
        _userContext = userContext;
    }

    // Abstract method that derived classes must implement to specify how to filter by user
    protected abstract Expression<Func<TEntity, bool>> GetUserFilter();

    // Get entity by ID, but only if it belongs to the current user
    public async Task<TEntity?> GetByIdForCurrentUserAsync(TEntityId id, CancellationToken cancellationToken = default)
    {
        var userFilter = GetUserFilter();
        
        return await DbContext
            .Set<TEntity>()
            .Where(entity => entity.Id == id)
            .Where(userFilter)
            .FirstOrDefaultAsync(cancellationToken);
    }

    // Get all entities for the current user
    public async Task<IEnumerable<TEntity>> GetAllForCurrentUserAsync(CancellationToken cancellationToken = default)
    {
        var userFilter = GetUserFilter();
        
        return await DbContext
            .Set<TEntity>()
            .Where(userFilter)
            .ToListAsync(cancellationToken);
    }

    // Get entities with additional filtering for the current user
    public async Task<IEnumerable<TEntity>> GetForCurrentUserAsync(
        Expression<Func<TEntity, bool>> additionalFilter, 
        CancellationToken cancellationToken = default)
    {
        var userFilter = GetUserFilter();
        
        return await DbContext
            .Set<TEntity>()
            .Where(userFilter)
            .Where(additionalFilter)
            .ToListAsync(cancellationToken);
    }

    // Paginated query for better performance with large datasets
    public async Task<(IEnumerable<TEntity> Items, int TotalCount)> GetPagedForCurrentUserAsync(
        int pageNumber,
        int pageSize,
        Expression<Func<TEntity, bool>>? additionalFilter = null,
        CancellationToken cancellationToken = default)
    {
        var userFilter = GetUserFilter();
        var query = DbContext.Set<TEntity>().Where(userFilter);
        
        if (additionalFilter != null)
            query = query.Where(additionalFilter);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (items, totalCount);
    }

    // Bulk operations for better performance
    public async Task<bool> BulkExistsForCurrentUserAsync(
        IEnumerable<TEntityId> ids, 
        CancellationToken cancellationToken = default)
    {
        var userFilter = GetUserFilter();
        var idList = ids.ToList();
        
        var count = await DbContext.Set<TEntity>()
            .Where(userFilter)
            .Where(entity => idList.Contains(entity.Id))
            .CountAsync(cancellationToken);
            
        return count == idList.Count;
    }

    // Check if entity exists and belongs to current user
    public async Task<bool> ExistsForCurrentUserAsync(TEntityId id, CancellationToken cancellationToken = default)
    {
        var userFilter = GetUserFilter();

        return await DbContext
            .Set<TEntity>()
            .Where(entity => entity.Id == id)
            .Where(userFilter)
            .AnyAsync(cancellationToken);
    }

    // Optimized projection-based queries to avoid loading full entities
    public async Task<TResult?> GetProjectedForCurrentUserAsync<TResult>(
        TEntityId id,
        Expression<Func<TEntity, TResult>> projection,
        CancellationToken cancellationToken = default)
    {
        var userFilter = GetUserFilter();
        
        return await DbContext.Set<TEntity>()
            .Where(entity => entity.Id == id)
            .Where(userFilter)
            .Select(projection)
            .FirstOrDefaultAsync(cancellationToken);
    }

    // Get multiple projections efficiently
    public async Task<IEnumerable<TResult>> GetProjectedForCurrentUserAsync<TResult>(
        Expression<Func<TEntity, TResult>> projection,
        Expression<Func<TEntity, bool>>? additionalFilter = null,
        CancellationToken cancellationToken = default)
    {
        var userFilter = GetUserFilter();
        var query = DbContext.Set<TEntity>().Where(userFilter);
        
        if (additionalFilter != null)
            query = query.Where(additionalFilter);

        return await query
            .Select(projection)
            .ToListAsync(cancellationToken);
    }

    // Verify ownership for update/delete operations - returns proper authorization result
    public async Task<AuthorizationResult> VerifyOwnershipAsync(TEntityId id, CancellationToken cancellationToken = default)
    {
        var userFilter = GetUserFilter();
        
        // Single query to check both existence and ownership
        var query = DbContext.Set<TEntity>()
            .Where(entity => entity.Id == id)
            .Select(entity => new { 
                Exists = true, 
                IsOwned = userFilter.Compile()(entity) 
            });

        var result = await query.FirstOrDefaultAsync(cancellationToken);
        
        if (result == null)
            return AuthorizationResult.NotFound;
            
        return result.IsOwned ? AuthorizationResult.Success : AuthorizationResult.Unauthorized;
    }

    // Get entity for update/delete with proper authorization
    public async Task<(TEntity? Entity, AuthorizationResult Result)> GetForUpdateAsync(
        TEntityId id,
        CancellationToken cancellationToken = default)
    {
        // First check if entity exists
        var entity = await DbContext
            .Set<TEntity>()
            .FirstOrDefaultAsync(e => e.Id == id, cancellationToken);

        if (entity is null)
            return (null, AuthorizationResult.NotFound);

        // Check ownership
        var userFilter = GetUserFilter();
        var compiled = userFilter.Compile();

        if (!compiled(entity))
            return (null, AuthorizationResult.Unauthorized);

        return (entity, AuthorizationResult.Success);
    }
}
