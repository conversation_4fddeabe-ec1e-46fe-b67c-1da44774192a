using Bookify.Domain.Entities.Abstractions;
using Bookify.Infrastructure.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bookify.Infrastructure.Extensions;

public static class AuthorizationResultExtensions
{
    public static IActionResult ToActionResult(this AuthorizationResult result)
    {
        return result switch
        {
            AuthorizationResult.Success => throw new InvalidOperationException("Success should not be converted to error result"),
            AuthorizationResult.NotFound => new NotFoundResult(),
            AuthorizationResult.Unauthorized => new UnauthorizedResult(),
            _ => throw new ArgumentOutOfRangeException(nameof(result))
        };
    }

    public static Result<T> ToResult<T>(this AuthorizationResult authResult)
    {
        return authResult switch
        {
            AuthorizationResult.Success => throw new InvalidOperationException("Success should not be converted to error result"),
            AuthorizationResult.NotFound => Result.Failure<T>(AuthorizationErrors.ResourceNotFound),
            AuthorizationResult.Unauthorized => Result.Failure<T>(AuthorizationErrors.Unauthorized),
            _ => throw new ArgumentOutOfRangeException(nameof(authResult))
        };
    }

    public static Error ToError(this AuthorizationResult result)
    {
        return result switch
        {
            AuthorizationResult.Success => throw new InvalidOperationException("Success should not be converted to error"),
            AuthorizationResult.NotFound => AuthorizationErrors.ResourceNotFound,
            AuthorizationResult.Unauthorized => AuthorizationErrors.Unauthorized,
            _ => throw new ArgumentOutOfRangeException(nameof(result))
        };
    }
}
