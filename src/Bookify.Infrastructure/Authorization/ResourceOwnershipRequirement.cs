using Bookify.Application.Abstractions.Authentication;
using Microsoft.AspNetCore.Authorization;

namespace Bookify.Infrastructure.Authorization;

public class ResourceOwnershipRequirement : IAuthorizationRequirement
{
    public ResourceOwnershipRequirement(string resourceType)
    {
        ResourceType = resourceType;
    }

    public string ResourceType { get; }
}

public class ResourceOwnershipHandler : AuthorizationHandler<ResourceOwnershipRequirement, IResourceOwnership>
{
    private readonly IServiceProvider _serviceProvider;

    public ResourceOwnershipHandler(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    protected override async Task HandleRequirementAsync(
        AuthorizationHandlerContext context,
        ResourceOwnershipRequirement requirement,
        IResourceOwnership resource)
    {
        if (!context.User.Identity.IsAuthenticated)
            return;

        using var scope = _serviceProvider.CreateScope();
        var userContext = scope.ServiceProvider.GetRequiredService<IUserContext>();

        if (resource.IsOwnedBy(userContext.UserId))
        {
            context.Succeed(requirement);
        }
    }
}

// Interface that resources must implement
public interface IResourceOwnership
{
    bool IsOwnedBy(Guid userId);
}
