using Asp.Versioning;
using Bookify.Application.Abstractions.Authentication;
using Bookify.Application.Bookings.GetBooking;
using Bookify.Application.Bookings.ReserveBooking;
using Bookify.Application.Bookings.UpdateBooking;
using Bookify.Domain.Abstractions.Mediator;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bookify.API.Controllers.Bookings;

// Enhanced controller demonstrating proper 401 vs 404 handling
[Authorize]
[ApiController]
[ApiVersion(ApiVersions.V1)]
[Route("api/v{version:apiVersion}/bookings-enhanced")]
public class BookingsControllerEnhanced : ControllerBase
{
    private readonly ISender _sender;
    private readonly IUserContext _userContext;

    public BookingsControllerEnhanced(ISender sender, IUserContext userContext)
    {
        _sender = sender;
        _userContext = userContext;
    }

    /// <summary>
    /// GET operation - Always returns 404 for both "not found" and "not owned" (security through obscurity)
    /// </summary>
    [HttpGet("{id}")]
    public async Task<IActionResult> GetBooking(Guid id, CancellationToken cancellationToken)
    {
        var query = new GetBookingQuery(id);
        var result = await _sender.Send(query, cancellationToken);

        if (result.IsFailure)
        {
            // For READ operations, always return 404 regardless of whether:
            // - Resource doesn't exist, OR
            // - Resource exists but belongs to another user
            // This prevents information disclosure
            return NotFound();
        }

        return Ok(result.Value);
    }

    /// <summary>
    /// POST operation - Creates booking for authenticated user only
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> ReserveBooking(
        ReserveBookingRequest request,
        CancellationToken cancellationToken)
    {
        var command = new ReserveBookingCommand(
            request.ApartmentId,
            _userContext.UserId,  // Always use authenticated user's ID
            request.StartDate,
            request.EndDate);

        var result = await _sender.Send(command, cancellationToken);

        if (result.IsFailure) 
            return BadRequest(result.Error);

        return CreatedAtAction(nameof(GetBooking), new { id = result.Value }, result.Value);
    }

    /// <summary>
    /// PUT operation - Returns 404 if doesn't exist, 401 if exists but not owned
    /// </summary>
    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateBooking(
        Guid id, 
        UpdateBookingRequest request,
        CancellationToken cancellationToken)
    {
        var command = new UpdateBookingCommand(id, request.StartDate, request.EndDate);
        var result = await _sender.Send(command, cancellationToken);

        if (result.IsFailure)
        {
            // For WRITE operations, distinguish between different error types
            return result.Error.Code switch
            {
                "Authorization.ResourceNotFound" => NotFound(),      // 404 - Booking doesn't exist
                "Authorization.Unauthorized" => Unauthorized(),     // 401 - Booking exists but belongs to another user
                _ => BadRequest(result.Error)                        // 400 - Other business logic errors
            };
        }

        return Ok();
    }

    /// <summary>
    /// DELETE operation - Returns 404 if doesn't exist, 401 if exists but not owned
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteBooking(Guid id, CancellationToken cancellationToken)
    {
        // var command = new DeleteBookingCommand(id);
        // var result = await _sender.Send(command, cancellationToken);

        // Placeholder implementation
        var result = new { IsFailure = false, Error = new { Code = "" } };

        if (result.IsFailure)
        {
            // For WRITE operations, distinguish between different error types
            return result.Error.Code switch
            {
                "Authorization.ResourceNotFound" => NotFound(),      // 404 - Booking doesn't exist
                "Authorization.Unauthorized" => Unauthorized(),     // 401 - Booking exists but belongs to another user  
                _ => BadRequest(result.Error)                        // 400 - Other business logic errors
            };
        }

        return NoContent();
    }
}

// Request models
public record UpdateBookingRequest(DateOnly StartDate, DateOnly EndDate);
