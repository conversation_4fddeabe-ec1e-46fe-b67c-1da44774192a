namespace Bookify.Domain.Entities.Abstractions;

public static class AuthorizationErrors
{
    public static readonly Error Unauthorized = new(
        "Authorization.Unauthorized",
        "You are not authorized to access this resource");

    public static readonly Error Forbidden = new(
        "Authorization.Forbidden", 
        "You do not have permission to perform this action");

    public static readonly Error ResourceNotFound = new(
        "Authorization.ResourceNotFound",
        "The requested resource was not found");

    public static readonly Error InvalidOwnership = new(
        "Authorization.InvalidOwnership",
        "You can only access resources that belong to you");
}
