﻿using Bookify.Domain.Entities.Apartments;
using Bookify.Domain.Entities.Bookings.ValueObjects;

namespace Bookify.Domain.Entities.Bookings;

public interface IBookingRepository
{
    Task<Booking> GetByIdAsync(BookingId id, CancellationToken cancellationToken = default);

    // User-aware method - only returns booking if it belongs to current user
    Task<Booking?> GetByIdForCurrentUserAsync(BookingId id, CancellationToken cancellationToken = default);

    // Get all bookings for current user
    Task<IEnumerable<Booking>> GetAllForCurrentUserAsync(CancellationToken cancellationToken = default);

    Task<bool> IsOverlappingAsync(
        Apartment apartment,
        DateRange duration,
        CancellationToken cancellationToken = default);

    void Add(Booking booking);
}
