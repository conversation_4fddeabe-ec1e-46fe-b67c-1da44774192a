using Bookify.Application.Abstractions.Authentication;
using System.Linq.Expressions;

namespace Bookify.Application.Abstractions.Specifications;

public abstract class UserOwnedSpecification<T> where T : class
{
    protected readonly IUserContext _userContext;

    protected UserOwnedSpecification(IUserContext userContext)
    {
        _userContext = userContext;
    }

    public abstract Expression<Func<T, bool>> ToExpression();
    
    protected Expression<Func<T, bool>> IsOwnedByCurrentUser<TEntity>(Expression<Func<TEntity, Guid>> userIdSelector)
        where TEntity : class
    {
        var parameter = Expression.Parameter(typeof(T), "x");
        var userIdProperty = Expression.Invoke(userIdSelector, parameter);
        var currentUserId = Expression.Constant(_userContext.UserId);
        var equality = Expression.Equal(userIdProperty, currentUserId);
        
        return Expression.Lambda<Func<T, bool>>(equality, parameter);
    }
}

// Example usage for bookings
public class UserBookingSpecification : UserOwnedSpecification<Booking>
{
    public UserBookingSpecification(IUserContext userContext) : base(userContext)
    {
    }

    public override Expression<Func<Booking, bool>> ToExpression()
    {
        return booking => booking.UserId == _userContext.UserId;
    }
}

// Generic repository method that applies user filtering
public interface IUserOwnedRepository<T> where T : class
{
    Task<T?> GetByIdForUserAsync(object id, CancellationToken cancellationToken = default);
    Task<IEnumerable<T>> GetAllForUserAsync(CancellationToken cancellationToken = default);
}
