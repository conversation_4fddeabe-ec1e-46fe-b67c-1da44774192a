using Bookify.Application.Abstractions.Authentication;
using Bookify.Application.Abstractions.Data;
using Bookify.Application.Abstractions.Messaging;
using Bookify.Domain.Entities.Abstractions;
using Bookify.Domain.Entities.Bookings;
using Dapper;

namespace Bookify.Application.Bookings.GetBooking;

// Option 2: SQL-Based Approach with User Context (Alternative)
internal sealed class GetBookingQueryHandlerSqlVersion : IQueryHandler<GetBookingQuery, BookingResponse>
{
    private readonly ISqlConnectionFactory _sqlConnectionFactory;
    private readonly IUserContext _userContext;

    public GetBookingQueryHandlerSqlVersion(ISqlConnectionFactory sqlConnectionFactory, IUserContext userContext)
    {
        _sqlConnectionFactory = sqlConnectionFactory;
        _userContext = userContext;
    }

    public async Task<Result<BookingResponse>> Handle(GetBookingQuery request, CancellationToken cancellationToken)
    {
        using var connection = _sqlConnectionFactory.CreateConnection();

        // SQL query that automatically includes user filtering - NO manual authorization check needed!
        const string sql = """
            SELECT
                id AS Id,
                apartment_id AS ApartmentId,
                user_id AS UserId,
                status AS Status,
                price_for_period_amount AS PriceAmount,
                price_for_period_currency AS PriceCurrency,
                cleaning_fee_amount AS CleaningFeeAmount,
                cleaning_fee_currency AS CleaningFeeCurrency,
                amenities_up_charge_amount AS AmenitiesUpChargeAmount,
                amenities_up_charge_currency AS AmenitiesUpChargeCurrency,
                total_price_amount AS TotalPriceAmount,
                total_price_currency AS TotalPriceCurrency,
                duration_start AS DurationStart,
                duration_end AS DurationEnd,
                created_on_utc AS CreatedOnUtc
            FROM bookings
            WHERE id = @BookingId 
              AND user_id = @CurrentUserId  -- 🔒 Automatic user filtering!
            """;

        var booking = await connection.QueryFirstOrDefaultAsync<BookingResponse>(
            sql,
            new
            {
                request.BookingId,
                CurrentUserId = _userContext.UserId  // 🔒 Always filter by current user
            });

        // No manual authorization check needed - SQL handles it!
        if (booking is null)
            return Result.Failure<BookingResponse>(BookingErrors.NotFound);

        return booking;
    }
}

// Extension method to make this pattern reusable
public static class UserAwareSqlExtensions
{
    public static string AddUserFilter(this string sql, string userIdColumn = "user_id")
    {
        return $"{sql} AND {userIdColumn} = @CurrentUserId";
    }
}

// Example usage:
// const string baseSql = "SELECT * FROM bookings WHERE id = @BookingId";
// const string userAwareSql = baseSql.AddUserFilter();
