using Bookify.Application.Abstractions.Messaging;
using Bookify.Domain.Entities.Abstractions;
using Bookify.Domain.Entities.Bookings;

namespace Bookify.Application.Bookings.GetBooking;

// Optimized version using projections to avoid loading full entities
internal sealed class GetBookingQueryHandlerOptimized : IQueryHandler<GetBookingQuery, BookingResponse>
{
    private readonly IBookingRepository _bookingRepository;

    public GetBookingQueryHandlerOptimized(IBookingRepository bookingRepository)
    {
        _bookingRepository = bookingRepository;
    }

    public async Task<Result<BookingResponse>> Handle(GetBookingQuery request, CancellationToken cancellationToken)
    {
        // Use projection to only load the data we need for the response
        var response = await _bookingRepository.GetProjectedForCurrentUserAsync(
            new BookingId(request.BookingId),
            booking => new BookingResponse
            {
                Id = booking.Id.Value,
                ApartmentId = booking.ApartmentId.Value,
                UserId = booking.UserId.Value,
                Status = (int)booking.Status,
                PriceAmount = booking.PriceForPeriod.Amount,
                PriceCurrency = booking.PriceForPeriod.Currency.Code,
                CleaningFeeAmount = booking.CleaningFee.Amount,
                CleaningFeeCurrency = booking.CleaningFee.Currency.Code,
                AmenitiesUpChargeAmount = booking.AmenitiesUpCharge.Amount,
                AmenitiesUpChargeCurrency = booking.AmenitiesUpCharge.Currency.Code,
                TotalPriceAmount = booking.TotalPrice.Amount,
                TotalPriceCurrency = booking.TotalPrice.Currency.Code,
                DurationStart = booking.Duration.Start,
                DurationEnd = booking.Duration.End,
                CreatedOnUtc = booking.CreatedOnUtc
            },
            cancellationToken);

        if (response is null)
            return Result.Failure<BookingResponse>(BookingErrors.NotFound);

        return response;
    }
}