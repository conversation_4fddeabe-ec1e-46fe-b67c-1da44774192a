using Bookify.Application.Abstractions.Messaging;
using Bookify.Domain.Entities.Abstractions;
using Bookify.Domain.Entities.Bookings;
using Bookify.Infrastructure.Authorization;
using Bookify.Infrastructure.Extensions;

namespace Bookify.Application.Bookings.UpdateBooking;

public record UpdateBookingCommand(
    Guid BookingId,
    DateOnly StartDate,
    DateOnly EndDate) : ICommand;

internal sealed class UpdateBookingCommandHandler : ICommandHandler<UpdateBookingCommand>
{
    private readonly IBookingRepository _bookingRepository;
    private readonly IUnitOfWork _unitOfWork;

    public UpdateBookingCommandHandler(IBookingRepository bookingRepository, IUnitOfWork unitOfWork)
    {
        _bookingRepository = bookingRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result> Handle(UpdateBookingCommand request, CancellationToken cancellationToken)
    {
        // Get booking with proper authorization check
        var (booking, authResult) = await _bookingRepository.GetForUpdateAsync(
            new BookingId(request.BookingId), 
            cancellationToken);

        // Handle authorization results properly
        if (authResult != AuthorizationResult.Success)
        {
            return Result.Failure(authResult.ToError());
        }

        // At this point, we know:
        // 1. Booking exists
        // 2. Current user owns the booking
        // 3. User is authorized to modify it

        // Perform business logic
        // booking.UpdateDates(request.StartDate, request.EndDate);

        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}

// Example controller usage
/*
[HttpPut("{id}")]
public async Task<IActionResult> UpdateBooking(Guid id, UpdateBookingRequest request)
{
    var command = new UpdateBookingCommand(id, request.StartDate, request.EndDate);
    var result = await _sender.Send(command);

    if (result.IsFailure)
    {
        // This will return:
        // - 404 Not Found if booking doesn't exist
        // - 401 Unauthorized if booking belongs to another user
        return result.Error.Code switch
        {
            "Authorization.ResourceNotFound" => NotFound(),
            "Authorization.Unauthorized" => Unauthorized(),
            _ => BadRequest(result.Error)
        };
    }

    return Ok();
}
*/
